[{"uri": "//localhost:1313/vi/1-introduce/", "title": "Giới thiệu", "tags": [], "description": "", "content": "Giới thiệu hệ thống Workshop này sẽ hướng dẫn bạn xây dựng và triển khai một website thương mại điện tử động sử dụng các dịch vụ serverless hiện đại của AWS. Bạn sẽ sử dụng các thành phần chủ chốt như API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch và SAM CLI để tự động hoá, mở rộng và tối ưu chi phí cho hệ thống.\nKiến trúc tổng thể Dưới đây là kiến trúc tổng thể của hệ thống.\n(<PERSON><PERSON><PERSON> sơ đồ kiến trúc tại đây)\nFrontend: Sử dụng theme Hugo, lưu trữ tĩnh trên Amazon S3 và phân phối qua CloudFront. Backend: Giao tiếp qua RESTful API với API Gateway, xử lý nghiệp vụ bởi AWS Lambda. Cơ sở dữ liệu: Sử dụng DynamoDB để lưu trữ dữ liệu động, mở rộng dễ dàng. Quản lý hạ tầng: CloudFormation tự động tạo &amp; cấu hình tài nguyên AWS. Sử dụng SAM CLI để triển khai và tự động hóa serverless. Tên miền &amp; Quản lý hoạt động: Rooute 53 quản lý tên miền/DNS. CloudWatch dùng để theo dõi, ghi nhận log và cảnh báo cho toàn bộ hệ thống. Sơ đồ luồng dữ liệu Sơ đồ sau minh họa luồng dữ liệu chính và tương tác giữa các thành phần hệ thống.\n(Chèn sơ đồ luồng dữ liệu hoặc sequence diagram tại đây)\nDanh sách dịch vụ AWS sử dụng API Gateway: Tạo REST API cho giao tiếp giữa frontend-backend. S3: Lưu trữ website tĩnh và các tài nguyên của website. Lambda: Xử lý logic backend serverless. DynamoDB: Co sở dữ liệu. CloudFormation: Quản lý hạ tầng tự động. Route 53: Đăng ký tên miền và DNS. CloudWatch: Giám sát hệ thống và cảnh báo, giúp debugging/tracking hiệu quả. SAM CLI: Tự động hóa triển khai tài nguyên serverless. Tính năng chính của website Kiến trúc serverless hoàn toàn trên AWS. Triển khai website động, bảo mật, mở rộng tự động. Giao diện thân thiện với Hugo. Backend động sử dụng API RESTful qua API Gateway &amp; Lambda. Cơ sở dữ liệu động với DynamoDB. Xử lý dữ liệu thời gian thực bằng Lambda &amp; DynamoDB. Tích hợp giám sát, logging và cấu hình DNS. Hạ tầng dưới dạng mã với CloudFormation &amp; SAM CLI. "}, {"uri": "//localhost:1313/vi/", "title": "Website thư<PERSON><PERSON> mại điện tử", "tags": [], "description": "", "content": "Xây dựng website thương mại điện tử động với AWS Serverless Tổng quan Trong Workshop này, bạn sẽ triển khai một website thương mại điện tử động sử dụng framework Hugo trên nền tảng điện toán đám mây AWS. Bạn sẽ học cách chuẩn bị môi trường, cấu hình tài khoản AWS, xây dựng website với Hugo, và triển khai toàn bộ hệ thống trên các dịch vụ chính của AWS như API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, đồng thời sử dụng SAM CLI để tự động hoá quá trình triển khai.\nMục tiêu của Workshop Hiểu và thao tác thành thạo các công cụ AWS cần thiết cho một dự án website động. Bi<PERSON>t cách chuẩn bị, c<PERSON><PERSON> đặt, cấu hình môi trường phát triển cho dự án Hugo. <PERSON><PERSON><PERSON> dự<PERSON>, đóng gói và triển khai website động với Hugo kết hợp các dịch vụ serverless hiện đại của AWS. Thiết kế và triển khai (deploy) với API Gateway, xử lý logic động bằng Lambda, lưu trữ dữ liệu trên DynamoDB, và quản lý website động trên S3. Sử dụng CloudFormation để tự động tạo và cấu hình tài nguyên AWS, theo dõi và giám sát hoạt động hệ thống với CloudWatch. Cấu hình tên miền và phân giải DNS với Route 53 để truy cập website động qua internet. Ứng dụng quy trình DevOps tự động hóa triển khai và vận hành website trên nền tảng AWS một cách hiệu quả. Kiến thức thu được sau Workshop Sau khi hoàn thành Workshop này, bạn sẽ:\nHiểu rõ kiến trúc và quy trình triển khai một website thương mại điện tử động trên nền tảng AWS. Học được cách sử dụng các dịch vụ AWS như API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch và SAM CLI trong một dự án thực tế. Biết cách xây dựng, đóng gói và triển khai website động với Hugo, kết nối frontend với backend qua API Gateway và Lambda. Thực hành quản lý dữ liệu động với DynamoDB, quản lý hạ tầng tự động bằng CloudFormation. Cấu hình tên miền với Route 53, giám sát hệ thống và log ứng dụng qua CloudWatch. Sẵn sàng áp dụng kiến thức vào các dự án thực tế, các bài toán về website động, serverless, hoặc DevOps trên AWS. Nội dung Giới thiệu Các bước chuẩn bị Triển khai backend Kiểm thử API backend với Postman Triển khai frontend Cấu hình tên miền &amp; theo dõi hệ thống Các câu hỏi thường gặp Dọn dẹp tài nguyên "}, {"uri": "//localhost:1313/vi/2-prerequiste/", "title": "<PERSON><PERSON><PERSON> bị môi trường", "tags": [], "description": "", "content": "Trước khi bắt đầu triển khai website thương mại điện tử động sử dụng các dịch vụ AWS như API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch và SAM CLI, bạn cần hoàn thành một số bước chuẩn bị môi trường cơ bản. Những công cụ và dịch vụ này sẽ giúp bạn triển khai ứng dụng một cách nhanh chóng, an toàn và hiệu quả.\nTrong phần này, bạn sẽ thực hiện các bước cài đặt và cấu hình môi trường phát triển cho cả frontend và backend của dự án:\nCài đặt Hugo: Để tạo và triển khai frontend cho website. Cài đặt NodeJS và Yarn: Để hỗ trợ việc xây dựng và quản lý frontend. Cài đặt SAM CLI: Đ<PERSON> triển khai backend serverless trên AWS. Tạo tài khoản AWS và cấu hình IAM: Để thiết lập quyền truy cập và bảo mật cho dự án trên AWS. Cấu hình Google OAuth2 Client ID và Client Secret: Để tích hợp đăng nhập Google bảo mật cho hệ thống. Các bước chuẩn bị này sẽ đảm bảo rằng bạn có môi trường đầy đủ và hoạt động chính xác trước khi tiến hành triển khai các dịch vụ AWS cho dự án của mình.\n⚠️ Lưu ý: Hãy chắc chắn rằng bạn đã cài đặt đầy đủ các công cụ trước khi bắt đầu làm việc với các dịch vụ AWS. Nếu không, bạn có thể gặp phải sự cố trong quá trình triển khai.\nNội dung Cài đặt NodeJS Cài đặt Yarn cho frontend Cài đặt SAM CLI cho backend Tạo tài khoản &amp; cấu hình IAM Tạo Google OAuth2 Project "}, {"uri": "//localhost:1313/vi/2-prerequiste/2.1-installnodejs/", "title": "Cài đặt NodeJS", "tags": [], "description": "", "content": "NodeJS là một môi trường chạy JavaScript mạnh mẽ và phổ biến, đư<PERSON><PERSON> sử dụng để phát triển các ứng dụng web động. Trong bước này, bạn sẽ cài đặt NodeJS trên các hệ điều hành để phát triển website thương mại điện tử động của mình.\nBước 1: Cài đặt NodeJS Tải NodeJS từ trang chính:\nTruy cập trang chính của NodeJS tại Node.js và tải phiên bản LTS cho hệ điều hành của bạn (Windows, macOS, hoặc Linux). Tải bản .msi cho Windows, .pkg cho macOS hoặc .tar.xz cho Linux. Cài đặt NodeJS trên Windows:\nSau khi tải file .msi, mở file và làm theo hướng dẫn cài đặt. Chắc chắn rằng bạn đã tích chọn Add to PATH trong quá trình cài đặt. Cài đặt NodeJS trên macOS:\nCài đặt thông qua Homebrew: brew install node Cài đặt NodeJS trên Linux (Ubuntu):\nSử dụng lệnh sau để cài đặt NodeJS:\nsudo apt install nodejs\rKiểm tra cài đặt:\nSau khi cài đặt, mở terminal (hoặc Command Prompt trên Windows) và nhập lệnh sau để kiểm tra phiên bản NodeJS:\nnode -v\rNếu cài đặt thành công, bạn sẽ thấy phiên bản NodeJS hiện ra.\nVí dụ\nSau khi cài đặt, mở terminal (hoặc Command Prompt trên Windows) và nhập lệnh sau để kiểm tra phiên bản NodeJS:\nC:\\Users\\<USER>"}, {"uri": "//localhost:1313/vi/3-tri%E1%BB%83n-khai-backend/", "title": "<PERSON><PERSON><PERSON> khai backend", "tags": [], "description": "", "content": "Triển khai backend cho website thương mại điện tử động được thực hiện bằng cách sử dụng AWS SAM CLI kết hợp với CloudFormation. Với các công cụ này, bạn sẽ dễ dàng triển khai các dịch vụ serverless như API Gateway, Lambda, và DynamoDB.\nCác công cụ chính được sử dụng: SAM CLI: Công cụ dòng lệnh giúp phát triển, kiểm tra và triển khai các ứng dụng serverless. SAM CLI giúp bạn dễ dàng triển khai API Gateway, Lambda function, và các tài nguyên serverless khác trên AWS. CloudFormation: Dịch vụ quản lý hạ tầng dưới dạng mã (Infrastructure as Code), cho phép bạn tự động hóa việc triển khai và quản lý các tài nguyên AWS. Trong phần này, bạn sẽ học cách triển khai backend serverless cho website của mình bằng SAM CLI, bao gồm việc tạo các tài nguyên như API Gateway, Lambda, và DynamoDB.\nLưu ý: Hãy đảm bảo rằng bạn đã hoàn thành các bước chuẩn bị môi trường (bao gồm cài đặt SAM CLI và cấu hình AWS IAM) trước khi bắt đầu triển khai backend.\nNội dung Triển khai backend bằng SAM CLI/CloudFormation Kiểm tra trạng thái và log backend sau khi triển khai "}, {"uri": "//localhost:1313/vi/2-prerequiste/2.2-installyarn/", "title": "Cài đặt Yarn", "tags": [], "description": "", "content": "Yarn là một trình quản lý gó<PERSON> (package manager) cho Java<PERSON>, được sử dụng rộng rãi trong các dự án frontend để giúp quản lý các thư viện và gói phần mềm một cách hiệu quả. Yarn giúp giải quyết các vấn đề về tốc độ cài đặt, sự nhất quán và bảo mật khi quản lý các thư viện cho dự án.\nBước 1: Cài đặt Yarn Cài đặt Yarn qua npm:\nYarn có thể được cài đặt thông qua npm (Node Package Manager). Đảm bảo rằng bạn đã cài đặt NodeJS và npm trước khi tiếp tục.\nĐể cài đặt Yarn, mở terminal hoặc Command Prompt và chạy lệnh sau:\nnpm install -g yarn\rCài đặt Yarn trên macOS (qua Homebrew):\nNếu bạn đang sử dụng macOS, bạn có thể cài đặt Yarn qua Homebrew bằng lệnh sau:\nbrew install yarn\rCài đặt Yarn trên Linux (Ubuntu):\nĐể cài đặt Yarn trên Ubuntu, bạn cần thêm kho lưu trữ của Yarn vào hệ thống và chạy lệnh sau:\nsudo apt update &amp;&amp; sudo apt install yarn\rYarn đã được cài đặt thành công! Bạn có thể bắt đầu quản lý các thư viện frontend cho dự án của mình.\nBước 2: Kiểm tra cài đặt Yarn Kiểm tra phiên bản Yarn:\nSau khi cài đặt thành công, bạn có thể kiểm tra phiên bản Yarn bằng lệnh:\nyarn --version\rNếu hiển thị ra phiên bản yarn, bạn đã cài đặt yarn thành công:\nC:\\Users\\<USER>"}, {"uri": "//localhost:1313/vi/4-testbackendapi/", "title": "<PERSON><PERSON><PERSON> thử API backend v<PERSON><PERSON>man", "tags": [], "description": "", "content": "Kiểm thử API backend với Postman Sau khi triển khai backend thành công với API Gateway và Lambda, bư<PERSON><PERSON> tiếp theo là kiểm thử các API để xác nhận hệ thống backend hoạt động đúng trước khi kết nối với frontend.\nPostman là công cụ mạnh mẽ và phổ biến nhất giúp bạn gửi các request (GET, POST, PUT, DELETE, &hellip;) đến endpoint API, xem kết quả trả về, kiểm tra logic xử lý cũng như debug lỗi.\nBạn có thể tải Postman tại: Download Postman\nMục tiêu phần này:\nHướng dẫn cách lấy endpoint API Gateway vừa deploy trên AWS để làm việc với Postman. Thực hành gửi các request GET/POST tới API, phân tích kết quả trả về để kiểm tra tính đúng đắn của backend. Nhận biết và xử lý một số lỗi phổ biến trong quá trình test API. Nội dung phần này bao gồm:\nLấy endpoint API Gateway:\nHướng dẫn tìm, copy URL endpoint trên AWS Console dùng cho việc kiểm thử API. Gửi request GET/POST để kiểm tra response backend:\nHướng dẫn chi tiết cách gửi các request GET/POST với Postman, nhập dữ liệu, kiểm tra kết quả response từ backend. Lưu ý:\nTrước khi test API bằng Postman, hãy chắc chắn rằng backend đã deploy thành công và không có lỗi ở các bước trước đó (CloudFormation/Lambda đều trạng thái thành công).\nKết thúc phần này, bạn sẽ:\nBiết cách lấy endpoint API Gateway để dùng cho kiểm thử. Sử dụng thành thạo Postman gửi các request GET/POST tới backend. Phân tích, kiểm tra kết quả trả về, và sẵn sàng kết nối frontend với backend đã kiểm thử thành công. Nội dung Lấy endpoint API Gateway Gửi request GET/POST để kiểm tra response backend "}, {"uri": "//localhost:1313/vi/3-tri%E1%BB%83n-khai-backend/3.1-deploy-backend/", "title": "Triển khai backend bằng SAM CLI/CloudFormation", "tags": [], "description": "", "content": "Triển khai Backend bằng SAM CLI/CloudFormation Trong phần này, bạn sẽ triển khai backend cho website thương mại điện tử động của mình bằng SAM CLI và CloudFormation. Chúng ta sẽ sử dụng file template.yaml có sẵn trong dự án để tự động tạo các tài nguyên như API Gateway, Lambda, và DynamoDB.\nBước 1: Cấu hình AWS CLI Trước khi bắt đầu sử dụng SAM CLI để triển khai, bạn cần phải cấu hình AWS CLI với các thông tin đăng nhập của tài khoản AWS. Điều này giúp SAM CLI có thể sử dụng các quyền truy cập đã được cấp cho IAM User của bạn.\nChạy lệnh aws configure:\nMở terminal và nhập lệnh sau để cấu hình AWS CLI:\naws configure\rNhập thông tin cấu hình:\nAWS Access Key ID: Nhập Access Key ID mà bạn đã tạo khi tạo IAM User.\nAWS Secret Access Key: Nhập Secret Access Key tương ứng với Access Key ID.\nDefault region name: Nhập ap-southeast-1 (cho Singapore).\nDefault output format: Bạn có thể nhập json , hoặc để mặc định None. Sau khi hoàn tất cấu hình, AWS CLI sẽ lưu thông tin cấu hình trong file ~/.aws/credentials (Linux/macOS) hoặc C:\\Users\\<USER>\\.aws\\credentials (Windows).\nBước 2: Triển khai Backend bằng SAM CLI Kiểm tra file template.yaml:\nNếu bạn chưa có file template.yaml, hãy kiểm tra trong thư mục dự án của bạn. File này sẽ định nghĩa các tài nguyên như API Gateway, Lambda, và DynamoDB. Nếu file này có sẵn trong dự án, đảm bảo rằng nó đã được cấu hình chính xác cho các tài nguyên bạn cần. Build dự án:\nSau khi đảm bảo file template.yaml đã có trong dự án\nTruy cập vào thư mục backend của dự án sử dụng SAM CLI để build dự án:\nsam build\rNếu bạn thấy dòng chữ Build Succeeded bạn đã cài đặt thành công SAM CLI Validate Template:\nKiểm tra lại cấu hình của file template.yaml để chắc chắn rằng không có lỗi cú pháp hoặc cấu hình:\nGõ lệnh sam validate để kiểm tra file template.yaml:\nsam validate\rNếu bạn thấy thông báo như bên dưới, thì file template.yaml của bạn hợp lệ. Triển khai tài nguyên lên AWS:\nSau khi hoàn tất build và validate, bạn có thể triển khai tài nguyên lên AWS bằng lệnh:\nsam deploy --guided\rKhi sử dụng SAM CLI để triển khai, bạn sẽ được yêu cầu nhập các thông tin cấu hình.\nStack Name: sam-app AWS Region: ap-southeast-1 Confirm changes before deploy: y Allow SAM CLI IAM role creation: y Disable rollback: n (Không tắt rollback) ExpressApiFunction has no authentication. Is this okay?: y Save arguments to configuration file: y SAM configuration file: Gõ phím Enter SAM configuration environment: Gõ phím Enter Khi sử dụng SAM CLI để triển khai, bạn sẽ được yêu cầu nhập các thông tin cấu hình.\nDeploy this changeset: y Bạn đợi khoảng 5 phút để cấu hình serverless (API Gateway, Lambda, DynamoDB, v.v.) từ máy tính của bạn lên AWS. Xác nhận triển khai:\nSau khi triển khai thành công, SAM CLI sẽ cung cấp thông tin về các tài nguyên đã được tạo. Bạn có thể kiểm tra API Gateway, Lambda function, S3, CLOUD FOUMATION và DynamoDB trong AWS Management Console để xác nhận các tài nguyên đã được tạo đúng. Backend của bạn đã được triển khai thành công! Các tài nguyên như API Gateway, Lambda function và DynamoDB đã được tự động tạo. Bạn có thể kiểm tra trên AWS Console.\nTruy cập vào API Gateway bạn sẽ thấy API Gateway đã được tạo Truy cập vào Lambda function bạn sẽ thấy Lambda function đã được tạo Truy cập vào Amazon S3 bạn sẽ thấy Amazon S3 đã được tạo Truy cập vào Cloud Foumation bạn sẽ thấy Cloud Foumation đã được tạo Truy cập vào DynamoDB bạn sẽ thấy DynamoDB đã được tạo "}, {"uri": "//localhost:1313/vi/2-prerequiste/2.3-installsamcli/", "title": "Cài đặt SAM CLI", "tags": [], "description": "", "content": "AWS SAM CLI là một công cụ dòng lệnh giúp bạn phát triển, đóng gói backend và triển khai các ứng dụng serverless sử dụng AWS Lambda, API Gateway và các dịch vụ AWS serverless khác. Trong bước này, bạn sẽ cài đặt SAM CLI để phát triển backend serverless cho website thương mại điện tử động của mình.\nBước 1: Cài đặt SAM CLI Cài đặt trên Windows: Truy cập vào trang Installing the AWS SAM CLI để tải bản cài đặt SAM CLI cho Windows. Click chọn Window Chạy tệp cài đặt .msi và làm theo các hướng dẫn để hoàn tất quá trình cài đặt. Mở Termiral gõ sam-version Cài đặt trên macOS:\nSử dụng Homebrew để cài đặt SAM CLI:\nbrew tap aws/tap\rbrew install aws-sam-cli\rCài đặt trên Linux (Ubuntu):\nTrên Linux, bạn có thể sử dụng apt để cài đặt SAM CLI:\nsudo apt-get update\rsudo apt-get install aws-sam-cli\rCài đặt SAM CLI từ nguồn (nếu cần):\nNếu bạn không muốn sử dụng các phương pháp trên, bạn có thể cài đặt SAM CLI từ mã nguồn. Hướng dẫn chi tiết có tại: Install SAM CLI from source. SAM CLI đã được cài đặt thành công! Bạn có thể bắt đầu phát triển và triển khai các ứng dụng serverless với AWS.\nBước 2: Kiểm tra cài đặt SAM CLI Kiểm tra phiên bản SAM CLI: Sau khi cài đặt thành công, mở terminal (hoặc Command Prompt trên Windows) và nhập lệnh sau để kiểm tra phiên bản SAM CLI:\nsam --version\rKiểm tra cài đặt SAM CLI: Nếu lệnh trên hiển thị thông tin về phiên bản của SAM CLI, bạn đã cài đặt thành công công cụ này.\nC:\\Users\\<USER>"}, {"uri": "//localhost:1313/vi/3-tri%E1%BB%83n-khai-backend/3.2-check-status-log-backend/", "title": "<PERSON><PERSON><PERSON> tra trạng thái và log backend sau khi triển khai", "tags": [], "description": "", "content": "Kiểm tra trạng thái và log backend sau khi triển khai Sau khi bạn đã deploy backend lên AWS với SAM CLI và CloudFormation, bước tiếp theo cực kỳ quan trọng là kiểm tra trạng thái tài nguyên và xem log backend để đảm bảo mọi thành phần đã hoạt động đúng.\nĐảm bảo API Gateway, Lambda, DynamoDB… đều được tạo thành công và đang hoạt động. Phát hiện và sửa lỗi kịp thời nếu gặp sự cố deploy hoặc lỗi logic. Chuẩn bị sẵn sàng cho bước kiểm thử API bằng Postman hoặc frontend. Kiểm tra trạng thái Stack và tài nguyên trên CloudFormation\nTruy cập AWS CloudFormation Console. Chọn Stack vừa deploy (sam-app). Kiểm tra tab Status: Nếu status là CREATE_COMPLETE hoặc UPDATE_COMPLETE bạn đã deploy thành công. Ở tab Resources, xem danh sách tài nguyên đã tạo (API Gateway, Lambda, DynamoDB, S3…). Nếu thấy status ROLLBACK, FAILED, click tab Events để xem chi tiết lỗi và xác định nguyên nhân.\nKiểm tra log Lambda bằng CloudWatch\nTrong AWS Console, vào Lambda → chọn hàm vừa deploy. Cuộn xuống và click vào tab Monitor, chọn vào View CloudWatch logs để truy cập log chi tiết. Cuộn xuống và click vào tab Log streams, chọn Log stream đầu tiên. Mỗi lần có request (GET/POST…), Lambda sẽ ghi log vào CloudWatch – tại đây bạn có thể:\nXem request đã nhận gì. Response trả về như thế nào. Debug lỗi nếu code bị crash hoặc trả về lỗi 500. Xác minh hoạt động của API Gateway\nTrong AWS Console, vào API Gateway → chọn API bạn vừa tạo. Vào mục Stages → chọn stage → chọn prod.\nCopy Invoke URL để chuẩn bị test bằng Postman/curl ở bước tiếp theo. Nếu bạn xác minh xong toàn bộ resource đều hoạt động tốt, log không có lỗi nghiêm trọng, bạn đã sẵn sàng test API backend bằng Postman hoặc tích hợp frontend!\n"}, {"uri": "//localhost:1313/vi/4-testbackendapi/4.1-api-gateway-endpoint/", "title": "Lấy endpoint API Gateway", "tags": [], "description": "", "content": "Lấy endpoint API Gateway Sau khi triển khai backend bằng SAM CLI/CloudFormation, hệ thống sẽ tự động tạo ra một địa chỉ endpoint cho API Gateway. Đây là địa chỉ bạn sẽ sử dụng để gửi các request kiể<PERSON> thử bằng Postman, curl hoặc tích hợp frontend sau này.\nNote:\nSử dụng endpoint của phần trước nếu bạn đã lưu\nCác bước thực hiện:\nTruy cập AWS Management Console\nĐăng nhập vào AWS Console. Chọn dịch vụ API Gateway trong danh sách dịch vụ AWS. Chọn API vừa deploy\nTại giao diện API Gateway, bạn sẽ thấy danh sách các API đã được tạo. Tìm và click vào tên API mà bạn vừa triển khai. Lấy endpoint ở mục Stages\nỞ thanh menu bên trái, chọn Stages. Click vào stage mà bạn đã deploy. Ng<PERSON> phía trên, bạn sẽ thấy Invoke URL. Đ<PERSON>y chính là endpoint của API Gateway mà bạn cần sử dụng cho việc kiểm thử. Copy URL này Kiểm tra lại endpoint\nBạn có thể copy endpoint này, dán vào trình duyệt hoặc Postman để kiểm tra phản hồi cơ bản. Thông thường, nếu chưa cấu hình resource cụ thể (ví dụ: /products), truy cập trực tiếp endpoint gốc sẽ báo lỗi “Missing Authentication Token” hoặc trả về HTTP 403. Điều này là bình thường, bạn chỉ cần thêm route resource đúng với API của mình khi test. Mẹo:\nNên lưu lại endpoint này ở file README dự án hoặc note cá nhân, tránh phải truy cập lại AWS Console nhiều lần khi test API.\n"}, {"uri": "//localhost:1313/vi/5-deployfrontend/", "title": "Triển khai frontend", "tags": [], "description": "", "content": "Triển khai frontend Sau khi backend đã sẵn sàng và đã kiểm thử thành công, bướ<PERSON> tiếp theo là triển khai phần frontend của website lên AWS S3 để tạo website tĩnh, giúp tối ưu chi phí và tốc độ tải trang.\n<PERSON><PERSON><PERSON> thời, bạn sẽ cấu hình hosting, cache, CORS để frontend hoạt động mượt mà, bảo mật khi kết nối với API backend.\nMục tiêu phần này:\nHướng dẫn upload và triển khai code frontend lên AWS S3 Bucket. Bật static website hosting, cấu hình cache và CORS để frontend truy cập ổn định, bảo mật. Kết nối frontend với endpoint API backend đã kiểm thử thành công ở các bước trước. Lưu ý:\n<PERSON><PERSON><PERSON> đảm bảo rằng bạn đã cài yarn trong thư mục dự án của mình\nKết thúc phần này, bạn sẽ:\n<PERSON>u khi thực hiện xong, bạn đã: Triển khai website frontend một cách bài bản trên S3, đảm bảo mọi người đều có thể truy cập. Làm chủ việc cấu hình hosting, cache, CORS – giúp website tải nhanh, an toàn và tương thích với backend. Sẵn sàng nâng cấp hệ thống với domain tùy chỉnh, CloudFront, hoặc các tính năng mới trong các bước tiếp theo. Nội dung Triển khai frontend lên S3 Bucket Bật static hosting, cấu hình cors Cấu hình Google OAuth2 Client ID và Client Secret Kết nối frontend với API backend "}, {"uri": "//localhost:1313/vi/4-testbackendapi/4.2-get-post-response/", "title": "Gửi request GET/POST để kiểm tra response backend", "tags": [], "description": "", "content": "G<PERSON>i request GET/POST để kiểm tra response backend đăng nhập Sau khi đã lấy được endpoint API Gateway, bạn cần gửi các request POST và GET đến API để xác nhận backend hoạt động đúng. Việ<PERSON> này thường thực hiện bằng Postman – công cụ test API phổ biến nhất hiện nay.\nCác bước thực hiện:\nKi<PERSON><PERSON> thử phương thức POST bằng Postman\nMở Postman, chọn New → HTTP Request.\nChọn method là POST.\nNhập endpoint đầy đủ vào ô URL, ví dụ:\nhttps://ars3538v1i.execute-api.ap-southeast-1.amazonaws.com/Prod/api/users <PERSON><PERSON>ể<PERSON> sang tab Body → chọn raw → chọn JSON. Nhập dữ liệu mẫu vào ô body như sau:\n{ &#34;username&#34;: &#34;AdminUser&#34;, &#34;email&#34;: &#34;<EMAIL>&#34;, &#34;password&#34;: &#34;AdminUser123@&#34;, &#34;firstName&#34;: &#34;Admin&#34;, &#34;lastName&#34;: &#34;User&#34;, &#34;isAdmin&#34;: true, &#34;googleId&#34;: null, &#34;avatar&#34;: &#34;https://i.imgur.com/avatar.jpg&#34;, &#34;phone&#34;: &#34;0912345678&#34;, &#34;address&#34;: &#34;&#34;, &#34;isDeleted&#34;: false } Nhấn Send để gửi request.\nNếu API hoạt động đúng, bạn sẽ nhận được response xác nhận.\nCopy lại token để sử dụng cho các request sau. Nếu API yêu cầu xác thực (API Key, Bearer Token, v.v.) hoặc cần custom header, hãy bổ sung ở tab Headers của Postman trước khi gửi request!\nKiểm thử phương thức GET đăng nhập bằng Postman\nTruy cập vào DynamoDB trên AWS Controller\nChọn table chọn bảng ShopUser Chọn Explore table items để kiểm tra dữ liệu của user vừa tạo Cuộn xuống và chỉnh sửa quyền isAdmin của user vừa tạo thành true Tạo một request mới trong Postman, chọn method GET.\nNhập Authorization đầy đủ vào ô URL, ví dụ:\nhttps://ars3538v1i.execute-api.ap-southeast-1.amazonaws.com/Prod/api/users Tại Authorization chọn: Bearer Token\nTại ô Token: nhập token đã lưu trước đó\nNhấn Send để gửi request. Nếu API hoạt động bình thường, bạn sẽ nhận được response trả về thông tin của các user đang tồn tại trong database.\nKiểm tra mã trạng thái HTTP (là 200 OK), nội dung body response. Bạn có thể test các chức năng khác của dự án bằng cách truy cập vào code để lấy url của api\nPhân tích kết quả và xử lý lỗi thường gặp\nNếu nhận được response dữ liệu đúng → backend đã hoạt động chính xác. Nếu gặp lỗi như 403 Forbidden, 401 Unauthorized, Missing Authentication Token: Kiểm tra lại endpoint đã đúng route resource chưa. Xem lại config API Gateway và mapping template. Kiểm tra log Lambda trên CloudWatch để xác định lỗi chi tiết. Nếu mã lỗi 500 Internal Server Error: Thường là do code Lambda gặp lỗi. Vào CloudWatch log để debug. Kết luận:\nViệc kiểm thử API bằng POST/GET là bước xác nhận backend đã hoạt động hoàn chỉnh trước khi tích hợp với frontend hoặc triển khai lên môi trường thật. Nếu gặp lỗi, nên tra log trên CloudWatch hoặc kiểm tra lại cấu hình API Gateway và Lambda để xử lý kịp thời.\n"}, {"uri": "//localhost:1313/vi/2-prerequiste/2.4-createiam/", "title": "Tạo tài k<PERSON>n và cấu hình IAM", "tags": [], "description": "", "content": "Tạo tài khoản và cấu hình IAM AWS Identity and Access Management (IAM) cho phép bạn quản lý quyền truy cập đến tài nguyên AWS một cách an toàn. Trong bư<PERSON><PERSON> n<PERSON>, bạn sẽ tạo một IAM user, cấp quyền truy cập thích hợp và cấu hình Access Key để sử dụng trong các bước tiếp theo của workshop.\nTạo IAM User Truy cập trang AWS Management Console và vào dịch vụ IAM. Trong thanh điều hướng bên trái, chọn Users và nhấn Create users ở menu bên trái. Tạo người dùng IAM: Đặt tên User name là AdminUser (hoặc tên bạn muốn). Tích chọn Provide user access to the AWS Management Console - optional Tại phần User type chọn I want to create an IAM user Chọn Custom password - Nhập mật khẩu của bạn Bỏ chọn Users must create a new password at next sign-in - Recommended Chọn Next Cấp quyền truy cập cho người dùng: Chọn Attach policies directly. Tìm kiếm và chọn policy AWSCloudFormationFullAccess, AmazonDynamoDBFullAccess, AWSLambda_FullAccess, AmazonS3FullAccess, AmazonAPIGatewayAdministrator, IAMFullAccess và tích chọn để thêm các quyền cần thiết cho dự án. Tiếp theo kéo xuống và nhấn Next Kiểm tra lại thông tin tại Permissions summary Nhấn Create user để tạo tài khoản AWS IAM Iam User đã được tạo thành công Chọn download .csv file để lưu trữ thông tin tài khoản Click view user để xem thông tin chi tiết Tạo Access Key Hiển thị và lưu Access Key và Secret Access Key: Chọn vào Create access key để tạo access key cho user. Tích chọn Command Line Interface (CLI) và nhấn Next. Click vào Show để hiển thị giá trị Secret access key. Click vào Create access key. Click vào Chọn Download .csv file để lưu trữ thông tin. Và bấm vào done Hãy lưu trữ Access key và Secret access key ở nơi an toàn. Chúng sẽ cần thiết cho việc truy cập các dịch vụ AWS qua API, CLI hoặc SDK.\nKiểm tra và thấy Access Key đã được tạo thành công. Lưu thông tin Access Key: Hãy lưu lại cặp giá trị Access key ID + Secret access key này vì bạn sẽ cần nó cho các bước tiếp theo của workshop. Bạn cũng có thể click Download .csv để tải xuống file chứa Access key ID và Secret access key dưới dạng CSV. "}, {"uri": "//localhost:1313/vi/5-deployfrontend/5.1-frontend-s3/", "title": "Triển khai frontend lên S3 Bucket", "tags": [], "description": "", "content": "Triển khai frontend lên S3 Bucket Để website frontend hoạt động online, bạn sẽ upload toàn bộ mã nguồn đã build lên AWS S3 Bucket. S3 sẽ đóng vai trò là hosting tĩnh, giúp phân phối website với chi phí thấp và tốc độ nhanh.\nCác bước thực hiện:\nBuild mã nguồn frontend\nMở termiral trên Vscode và truy cập vào thư mục frontend. Copy lệnh sau và dán vào termiral yarn build Th<PERSON> mục build sẽ chứa tất cả file tĩnh cần upload. Bạn đợi khoảng 1 phút để build dự án Tạo S3 Bucket mới trên AWS\nTruy cập AWS S3 Console. Click Create bucket. Đặt tên bucket, ví dụ: my-frontend-bucket-2025. Region: Chọn cùng region với backend (nên là ap-southeast-1 cho đồng bộ). Tắ<PERSON> “Block all public access”. Tích chọn vào I acknowledge that the current settings might result in this bucket and the objects within becoming public. , xác nhận cảnh báo để website có thể public Nhấn Create bucket để hoàn tất. Cẩn thận:\nBucket chứa website tĩnh cần mở quyền public read cho tất cả mọi người. Tuy nhiên không nên upload dữ liệu nhạy cảm vào đây vì bất cứ ai có link đều truy cập được!\nCấp quyền public read cho bucket\nSau khi Bucket đã được tạo thành công, chúng ta sẽ tiến hành cấp quyền cho Bucket. Click vào Bucket vừa tạo Truy cập Permissions của bucket Cuộn xuống dưới tìm mục Bucket policy. Chọn Edit Thêm policy cho phép public đọc file, ví dụ:\n{ &#34;Version&#34;: &#34;2012-10-17&#34;, &#34;Statement&#34;: [ { &#34;Sid&#34;: &#34;PublicReadGetObject&#34;, &#34;Effect&#34;: &#34;Allow&#34;, &#34;Principal&#34;: &#34;*&#34;, &#34;Action&#34;: &#34;s3:GetObject&#34;, &#34;Resource&#34;: &#34;arn:aws:s3:::my-frontend-bucket-2025/*&#34; } ] } Nhấn Save để áp dụng.\nUpload mã nguồn build lên bucket bằng AWS CLI\nĐảm bảo bạn đã cài đặt và cấu hình AWS CLI (đã chạy aws configure).\nThực hiện lệnh:\ncd frontend\raws s3 cp build/ s3://my-frontend-bucket-2025/ --recursive Tham số &ndash;recursive giúp upload tất cả file và folder con bên trong build.\nKiểm tra lại nội dung bucket\nVào lại bucket trên AWS Console, xác nhận toàn bộ file (index.html, main.js, CSS, ảnh…) đã được upload thành công.\nCó thể click trực tiếp vào file (VD: index.html) và copy URL tại Object URL để kiểm tra file đã public chưa (phải xem được file HTML/raw trên trình duyệt).\nBạn có thể kiểm tra các file và folder đã upload lên s3 bằng cách truy cập vào Object\nKết luận:\nSau khi hoàn thành các bước trên, bạn đã đưa website frontend của mình lên AWS S3 và sẵn sàng bật static hosting ở bước tiếp theo.\n"}, {"uri": "//localhost:1313/vi/6-cleanup/", "title": "D<PERSON>n dẹp tài nguyên  ", "tags": [], "description": "", "content": "Chúng ta sẽ tiến hành các bước sau để xóa các tài nguyên chúng ta đã tạo trong bài thực hành này.\nXóa EC2 instance Truy cập giao diện quản trị dịch vụ EC2 Click Instances. Click chọn cả 2 instance Public Linux Instance và Private Windows Instance. Click Instance state. Click Terminate instance, sau đó click Terminate để xác nhận. Truy cập giao diện quản trị dịch vụ IAM Click Roles. T<PERSON><PERSON> ô tìm kiếm , điền SSM. Click chọn SSM-Role. Click Delete, sau đó điền tên role SSM-Role và click Delete để xóa role. Click Users. Click chọn user Portfwd. Click Delete, sau đó điền tên user Portfwd và click Delete để xóa user. Xóa S3 bucket Truy cập giao diện quản trị dịch vụ System Manager - Session Manager. Click tab Preferences. Click Edit. Kéo chuột xuống dưới. Tại mục S3 logging. Bỏ chọn Enable để tắt tính năng logging. Kéo chuột xuống dưới. Click Save. Truy cập giao diện quản trị dịch vụ S3 Click chọn S3 bucket chúng ta đã tạo cho bài thực hành. ( Ví dụ : lab-fcj-bucket-0001 ) Click Empty. Điền permanently delete, sau đó click Empty để tiến hành xóa object trong bucket. Click Exit. Sau khi xóa hết object trong bucket, click Delete Điền tên S3 bucket, sau đó click Delete bucket để tiến hành xóa S3 bucket. Xóa các VPC Endpoint Truy cập vào giao diện quản trị dịch vụ VPC Click Endpoints. Chọn 4 endpoints chúng ta đã tạo cho bài thực hành bao gồm SSM, SSMMESSAGES, EC2MESSAGES, S3GW. Click Actions. Click Delete VPC endpoints. Tại ô confirm , điền delete. Click Delete để tiến hành xóa các endpoints. Click biểu tượng refresh, kiểm tra tất cả các endpoints đã bị xóa trước khi làm bước tiếp theo. Xóa VPC Truy cập vào giao diện quản trị dịch vụ VPC Click Your VPCs. Click chọn Lab VPC. Click Actions. Click Delete VPC. Tại ô confirm, điền delete để xác nhận, click Delete để thực hiện xóa Lab VPC và các tài nguyên liên quan. "}, {"uri": "//localhost:1313/vi/2-prerequiste/2.5-create-google-oauth2/", "title": "Tạo Google OAuth2 Project", "tags": [], "description": "", "content": "Tạo Google OAuth2 Project Để tích hợp chức năng đăng nhập Google (Google Sign-In) cho website thương mại điện tử, bạn cần đăng ký tạo 1 project Google OAuth2 Project.\nCác bước thực hiện:\n<PERSON><PERSON><PERSON> cập Google Cloud Console\nMở Google Cloud Console. Đăng nhập bằng tài khoản Google của bạn. Tạo mới một Project (nếu chưa có)\nNhấn Select a project &gt; New Project. Đặt tên project, chọn vị trí, nhấn Create. Tạo project thành công - click Select a project. Chọn vào project FcjFashionShop vừa tạo. Kích hoạt API Google OAuth2\nTại Quick access chọn vào mục API &amp; Services.\nHoặc trong menu bên trái, chọn APIs &amp; Services Chọn Library. Gõ Google+ API vào ô tìm kiếm và nhấn Enter. Chọn vào dịch vụ Google+ API. Bạn chọn Enable. Bạn chỉ cần tạo Google OAuth2 Project và enable các API cần thiết ở bước chuẩn bị này. Việc tạo OAuth Consent Screen và cấu hình Client ID/Client Secret sẽ thực hiện ở các bước tiếp theo sau khi đã có domain/frontend URL cụ thể.\n"}, {"uri": "//localhost:1313/vi/5-deployfrontend/5.2-enable-static-hosting/", "title": "Bật static hosting, c<PERSON>u hình cors", "tags": [], "description": "", "content": "Bật static hosting, cấ<PERSON> hình cors <PERSON>u khi đã upload mã nguồn frontend lên S3 Bucket, bạn cần bật tính năng static website hosting, cấu hình file index, error, thiết lập policy CORS và cache để website chạy ổn định, truy cập được từ trình duyệt và có thể kết nối tới API backend.\nCác bước thực hiện:\nBật static website hosting cho bucket\nTruy cập vào bucket frontend trên AWS S3 Console.\nChọn tab Properties.\nKéo xuống phần Static website hosting, nhấn Edit. Chọn Enable.\nNhập tên file index.html cho trường Index document.\nCó thể nhập thêm index.html cho trường Error document n.\nLưu lại cài đặt. AWS sẽ sinh ra một Website endpoint dạng:\nhttp://my-frontend-bucket-2025.s3-website-ap-southeast-1.amazonaws.com Copy URL này để dùng cho bước sau Bạn phải dùng đúng Website endpoint của S3 để truy cập trang web tĩnh, không dùng link Object URL.\nCấu hình CORS cho bucket\nVào tab Permissions → chọn CORS configuration. Thêm cấu hình mẫu sau để cho phép frontend gọi API backend trên domain khác: &lt;CORSConfiguration&gt; &lt;CORSRule&gt; &lt;AllowedOrigin&gt;*&lt;/AllowedOrigin&gt; &lt;AllowedMethod&gt;GET&lt;/AllowedMethod&gt; &lt;AllowedMethod&gt;HEAD&lt;/AllowedMethod&gt; &lt;AllowedMethod&gt;PUT&lt;/AllowedMethod&gt; &lt;AllowedMethod&gt;POST&lt;/AllowedMethod&gt; &lt;AllowedMethod&gt;DELETE&lt;/AllowedMethod&gt; &lt;AllowedHeader&gt;*&lt;/AllowedHeader&gt; &lt;/CORSRule&gt; &lt;/CORSConfiguration&gt; Nếu muốn giới hạn chỉ cho domain cụ thể (an toàn hơn): &lt;CORSConfiguration&gt; &lt;CORSRule&gt; &lt;AllowedOrigin&gt;https://your-domain.com&lt;/AllowedOrigin&gt; &lt;AllowedMethod&gt;GET&lt;/AllowedMethod&gt; &lt;AllowedHeader&gt;*&lt;/AllowedHeader&gt; &lt;/CORSRule&gt; &lt;/CORSConfiguration&gt; Nhấn Save. Lưu ý:\nĐể bảo mật tốt, khi triển khai thật, bạn nên giới hạn trường &lt;AllowedOrigin&gt; đúng với domain frontend, không nên để dấu * quá rộng!\nKiểm tra hoạt động website\nDùng link Website endpoint truy cập thử website. Nếu index.html tải thành công, website đã hoạt động trên S3. Nếu gặp lỗi 403/404: Kiểm tra lại quyền bucket (bucket policy). Đảm bảo đã bật static hosting. Xem lại tên file index/error đúng chính tả. Kết luận:\nBật static hosting và cấu hình cache/CORS đúng giúp website frontend trên S3 hoạt động ổn định, gọi API backend được, và tối ưu tốc độ tải trang cho người dùng.\n"}, {"uri": "//localhost:1313/vi/5-deployfrontend/5.3-clientid-clientserver/", "title": "<PERSON><PERSON><PERSON> hình Google OAuth2 Client ID và Client Secret", "tags": [], "description": "", "content": "<PERSON><PERSON><PERSON> hình Google OAuth2 Client ID và Client Secret Sau khi đã tạo Google OAuth2 Project và bật các API cần thiết ở bước chuẩn bị môi trường, bạn cần tạo OAuth2 Client ID và Client Secret để tích hợp đăng nhập Google cho hệ thống thương mại điện tử.\n<PERSON><PERSON><PERSON> bước thực hiện:\nT<PERSON><PERSON> cập vào Google Cloud Console\nMở Google Cloud Console và chọn đúng project đã tạo ở bước trước. Tạo OAuth2 Client ID\nVào APIs &amp; Services → Credentials. Click vào + Create Credentials → OAuth client ID. Click vào Configure consent srcreen. Click vào Get Started. Bạn điền các thông tin như sau:\nApp name: FcjFashionShop\nUser support email: Nhập email của bạn\nNhấn vào Next Email addresses: <PERSON>hập email của bạn.\nNhấn vào Next Tích chọn I agree to the Google API Services: User Data Policy.\nChọn Continue và chọn Create Tại Metrics click vào Create OAuth client Tại Application type chọn Web application\nName: FcjFashionShop Tại Authorized JavaScript origins\nChọn Add URI để thêm URl mới Dán URL của S3 Bucket website endpoint bạn đã copy trước đó Nếu chưa cấu hình OAuth consent screen:\nLàm theo hướng dẫn, nhập tên app, email, logo (nếu có), lưu lại. Chọn loại ứng dụng:\nChọn Web application (nếu website) hoặc Other/SPA nếu là ứng dụng đơn trang. Điền Authorized redirect URIs (cần chính xác):\nNếu dùng backend: https://your-backend-domain.com/api/auth/google/callback Nếu test local: http://localhost:3000/api/auth/google/callback Thêm domain S3 nếu frontend cũng xác thực trực tiếp với Google. Lấy Client ID và Client Secret\nSau khi tạo, Google sẽ hiển thị Client ID và Client Secret. Sao chép lại, lưu trữ cẩn thận. Thông tin này sẽ cần nhập vào: Biến môi trường backend (GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET) File cấu hình frontend nếu xác thực phía client. Cấu hình vào project\nVới backend (Node.js, Java, &hellip;): Thêm vào file .env: GOOGLE_CLIENT_ID=xxx.apps.googleusercontent.com\rGOOGLE_CLIENT_SECRET=xxxxxxxxxxxxxx Với frontend (React, Vue&hellip;): Thường chỉ cần Client ID (tuyệt đối không để lộ Client Secret trên client!) Thêm vào biến môi trường (nếu cần): REACT_APP_GOOGLE_CLIENT_ID=xxx.apps.googleusercontent.com Lưu ý bảo mật:\nKhông public Client Secret lên Github hoặc bất cứ đâu!\nChỉ sử dụng Client ID cho phía frontend, Client Secret dùng cho phía backend/server.\nMẹo:\nNên tạo riêng các Client ID cho môi trường dev, staging, production để dễ quản lý và bảo mật.\nKết luận:\nSau khi hoàn thành các bước này, bạn đã có đủ thông tin để cấu hình Google OAuth2 cho cả backend và frontend, sẵn sàng triển khai tính năng đăng nhập Google an toàn, chuyên nghiệp cho dự án thương mại điện tử trên AWS.\n"}, {"uri": "//localhost:1313/vi/5-deployfrontend/5.4-connect-frontend-api-backend/", "title": "<PERSON><PERSON>t nối frontend với API backend", "tags": [], "description": "", "content": "Kết nối frontend với API backend Sau khi đã upload website lên S3 và bật static hosting thành công, bước cuối cùng là kết nối frontend với backend thông qua endpoint API Gateway mà bạn đã kiểm thử trước đó. Đ<PERSON>y là thao tác cực kỳ quan trọng để website có thể lấy/gửi dữ liệu động và thực sự hoạt động như một ứng dụng thương mại điện tử hiện đại.\nCác bước thực hiện:\nXác định endpoint API backend\nSử dụng chính endpoint API Gateway đã lấy ở bước kiểm thử backend.\nVí dụ: https://abcd1234.execute-api.ap-southeast-1.amazonaws.com/dev/ Cập nhật cấu hình endpoint vào mã nguồn frontend\nVới React/Vue/Angular, thường sẽ có file cấu hình .env hoặc biến môi trường để định nghĩa URL backend. Ví dụ (React): Tạo hoặc cập nhật file .env trong thư mục dự án: REACT_APP_API_URL=https://abcd1234.execute-api.ap-southeast-1.amazonaws.com/dev/ Trong code, sử dụng biến này khi gọi API: fetch(`${process.env.REACT_APP_API_URL}users`, { ... }) Build lại frontend để các thay đổi có hiệu lực: yarn build Upload lại bản build mới lên S3 như đã hướng dẫn ở các bước trước. Kiểm thử fetch API từ frontend\nTruy cập website bằng đường dẫn S3 static hosting. Thực hiện thao tác fetch dữ liệu (VD: đăng nhập, lấy danh sách sản phẩm&hellip;) trên giao diện. Mở Developer Tools (F12) → tab Network để kiểm tra request API gửi đi và kết quả trả về. Nếu thấy dữ liệu trả về đúng từ backend, việc kết nối đã thành công. Xử lý các lỗi phổ biến khi kết nối\nLỗi CORS (Cross-Origin Resource Sharing): Nếu thấy lỗi liên quan đến CORS khi gọi API (bị chặn bởi trình duyệt), kiểm tra lại: Policy CORS trên API Gateway (và cả trên bucket S3 nếu fetch file). Đảm bảo header Access-Control-Allow-Origin đã trả về đúng domain frontend hoặc dùng dấu * (nếu test nội bộ). Lỗi 403/404 hoặc không trả về dữ liệu: Kiểm tra lại endpoint URL, route resource đã đúng chưa. Kiểm tra log Lambda trên CloudWatch để xác định lỗi backend nếu có. Website không load version mới: Có thể do cache trình duyệt, hãy refresh mạnh (Ctrl+F5) hoặc xóa cache, hoặc đặt lại cache-control cho index.html. Mẹo:\nNên cấu hình biến môi trường backend API endpoint, tránh hardcode trực tiếp vào source code để dễ thay đổi khi chuyển môi trường (dev, staging, production).\nKết luận:\nSau khi kết nối frontend với backend thành công, website đã sẵn sàng hoạt động động với dữ liệu thực tế từ AWS. Bạn có thể tiếp tục cấu hình domain, bảo mật SSL, hoặc triển khai CloudFront để tối ưu hơn.\n"}, {"uri": "//localhost:1313/vi/categories/", "title": "Categories", "tags": [], "description": "", "content": ""}, {"uri": "//localhost:1313/vi/tags/", "title": "Tags", "tags": [], "description": "", "content": ""}]