[{"uri": "//localhost:1313/vi/4-s3log/4.1-updateiam<PERSON>e/", "title": "<PERSON><PERSON><PERSON><PERSON> IAM Role", "tags": [], "description": "", "content": "Để các EC2 instance củ<PERSON> chúng ta có thể gửi session log tới S3 bucket , chúng ta sẽ cần cập nhật IAM Role đã gán vào EC2 instance bằng cách thêm vào policy cho phép quyền truy cập vào S3.\nCập nhật IAM Role Truy cập vào giao diện quản trị dịch vụ IAM Click Roles. T<PERSON><PERSON> ô tìm kiếm , điền SSM. Click vào role SSM-Role. Click Attach policies. Tại ô Search điền S3. Click chọn policy AmazonS3FullAccess. Click Attach policy. Trong thực tế chúng ta sẽ cấp quyền chặt chẽ hơn tới S3 bucket chỉ định. Trong khuôn khổ bài lab này chúng ta sử dụng policy AmazonS3FullAccess cho tiện dụng.\nTiếp theo chúng ta sẽ tiến hành tạo S3 bucket để lưu trữ session logs.\n"}, {"uri": "//localhost:1313/vi/1-introduce/", "title": "Giới thiệu", "tags": [], "description": "", "content": "Giới thiệu hệ thống Workshop này sẽ hướng dẫn bạn xây dựng và triển khai một website thương mại điện tử động sử dụng các dịch vụ serverless hiện đại của AWS. Bạn sẽ sử dụng các thành phần chủ chốt như API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch và SAM CLI để tự động hoá, mở rộng và tối ưu chi phí cho hệ thống.\nKiến trúc tổng thể Dưới đây là kiến trúc tổng thể của hệ thống.\n(<PERSON><PERSON><PERSON> sơ đồ kiến trúc tại đây)\nFrontend: Sử dụng theme Hugo, lưu trữ tĩnh trên Amazon S3 và phân phối qua CloudFront. Backend: Giao tiếp qua RESTful API với API Gateway, xử lý nghiệp vụ bởi AWS Lambda. Cơ sở dữ liệu: Sử dụng DynamoDB để lưu trữ dữ liệu động, mở rộng dễ dàng. Quản lý hạ tầng: CloudFormation tự động tạo &amp; cấu hình tài nguyên AWS. Sử dụng SAM CLI để triển khai và tự động hóa serverless. Tên miền &amp; Quản lý hoạt động: Rooute 53 quản lý tên miền/DNS. CloudWatch dùng để theo dõi, ghi nhận log và cảnh báo cho toàn bộ hệ thống. Sơ đồ luồng dữ liệu Sơ đồ sau minh họa luồng dữ liệu chính và tương tác giữa các thành phần hệ thống.\n(Chèn sơ đồ luồng dữ liệu hoặc sequence diagram tại đây)\nDanh sách dịch vụ AWS sử dụng API Gateway: Tạo REST API cho giao tiếp giữa frontend-backend. S3: Lưu trữ website tĩnh và các tài nguyên của website. Lambda: Xử lý logic backend serverless. DynamoDB: Co sở dữ liệu. CloudFormation: Quản lý hạ tầng tự động. Route 53: Đăng ký tên miền và DNS. CloudWatch: Giám sát hệ thống và cảnh báo, giúp debugging/tracking hiệu quả. SAM CLI: Tự động hóa triển khai tài nguyên serverless. Tính năng chính của website Kiến trúc serverless hoàn toàn trên AWS. Triển khai website động, bảo mật, mở rộng tự động. Giao diện thân thiện với Hugo. Backend động sử dụng API RESTful qua API Gateway &amp; Lambda. Cơ sở dữ liệu động với DynamoDB. Xử lý dữ liệu thời gian thực bằng Lambda &amp; DynamoDB. Tích hợp giám sát, logging và cấu hình DNS. Hạ tầng dưới dạng mã với CloudFormation &amp; SAM CLI. "}, {"uri": "//localhost:1313/vi/2-prerequiste/2.1-installhugo/", "title": "<PERSON>", "tags": [], "description": "", "content": "<PERSON><PERSON>i đặt <PERSON> là một static site generator n<PERSON>h chóng, mạnh mẽ và dễ sử dụng. Trong bước nà<PERSON>, bạn sẽ cài đặt <PERSON> để xây dựng và triển khai giao diện cho website thương mại điện tử động của mình.\nBước 1: <PERSON><PERSON><PERSON> đặt Hugo Tải và cài đặt Hugo:\nT<PERSON>y cập vào trang chính của Hugo tại: <PERSON> Releases. Chọn phiên bản phù hợp với hệ điều hành của bạn (Windows, macOS, hoặc Linux). Tải file .zip hoặc .tar.gz về và giải nén vào thư mục bạn chọn. Cài đặt Hugo trên Windows:\nTải bản .zip của Hugo từ trang Hugo for Windows. Giải nén và di chuyển tệp hugo.exe vào thư mục mà bạn dễ dàng truy cập (ví dụ: C:\\Hugo). Thêm đường dẫn thư mục vào PATH: Vào Control Panel → System and Security → System → Advanced system settings. Nhấn Environment Variables → Chọn Path và nhấn Edit. Thêm thư mục chứa hugo.exe vào. Kiểm tra cài đặt:\nMở Command Prompt và nhập lệnh sau: hugo version Nếu mọi thứ cài đặt thành công, bạn sẽ thấy thông tin phiên bản Hugo hiện ra. Cài đặt Hugo trên macOS:\nCài đặt qua Homebrew: brew install hugo Cài đặt Hugo trên Linux (Ubuntu):\nSử dụng lệnh apt: sudo apt-get install hugo Hugo đã được cài đặt thành công! Bạn có thể bắt đầu tạo website của mình với theme đã chọn.\nBước 2: Tạo Project Hugo Mới Sau khi cài đặt thành công Hugo, bạn có thể tạo một project Hugo mới với các bước sau:\nTạo thư mục cho project:\nMở terminal (hoặc Command Prompt trên Windows) và di chuyển đến thư mục bạn muốn tạo project. Chạy lệnh sau để tạo project Hugo mới: hugo new site my-ecommerce-website Lệnh này sẽ tạo một thư mục my-ecommerce-website chứa các file cấu hình và thư mục cần thiết cho dự án. Chọn và cài đặt theme:\nTruy cập trang Hugo Themes và chọn một theme mà bạn thích. Cài đặt theme bằng cách clone repository của theme vào thư mục themes trong project của bạn: cd my-ecommerce-website git init git submodule add https://github.com/gohugoio/hugo-theme-ananke.git themes/ananke Chạy thử website:\nSau khi cài đặt theme, bạn có thể chạy Hugo để xem website của mình trên máy local bằng cách sử dụng lệnh sau: hugo server Truy cập vào http://localhost:1313 để xem website. Các bước tiếp theo Sau khi cài đặt và chạy Hugo thành công, bạn có thể bắt đầu chỉnh sửa nội dung, cấu hình các trang và thêm các tính năng cho website thương mại điện tử của mình.\nLưu ý quan trọng: Đảm bảo rằng bạn đã cài đặt Hugo đúng cách trên hệ thống của mình. Nếu có vấn đề, hãy kiểm tra lại đường dẫn và cài đặt. Hugo là một công cụ mạnh mẽ cho việc xây dựng các website tĩnh, nhưng trong dự án này, bạn sẽ kết hợp nó với các dịch vụ AWS để triển khai website động. Hãy tiếp tục với các bước tiếp theo để cài đặt các công cụ cần thiết cho frontend và backend.\n"}, {"uri": "//localhost:1313/vi/3-accessibilitytoinstances/3.1-public-instance/", "title": "<PERSON><PERSON><PERSON><PERSON> nối đến máy chủ Public", "tags": [], "description": "", "content": "\nTruy cập vào giao diện quản trị của dịch vụ EC2. Click chọn Public Linux Instance. Click Actions. Click Security. Click Modify IAM role. Tại trang Modify IAM role. Click chọn SSM-Role. Click Save. Bạn sẽ cần chờ khoảng 10 phút trước khi thực hiện bước tiếp theo. Thời gian này EC2 instance của chúng ta sẽ tự động đăng ký với Session Manager.\nTruy cập vào giao diện quản trị của dịch vụ AWS Systems Manager Kéo thanh trượt menu bên trái xuống dưới. Click Session Manager. Click Start Session. Sau đó chọn Public Linux Instance và click Start session để truy cập vào instance. Terminal sẽ xuất hiện trên trình duyệt. Kiểm tra với câu lệnh sudo tcpdump -nn port 22 và sudo tcpdump chúng ta sẽ thấy không có traffic của SSH mà chỉ có traffic HTTPS. Ở trên, chúng ta đã tạo kết nối vào public instance mà không cần mở cổng SSH 22, giúp cho việc bảo mật tốt hơn, tránh mọi sự tấn công tới cổng SSH.\nMột nhược điểm của cách làm trên là ta phải mở Security Group outbound ở cổng 443 ra ngoài internet. Vì là public instance nên có thể sẽ không vấn đề gì nhưng nếu bạn muốn bảo mật hơn nữa, bạn có thể khoá cổng 443 ra ngoài internet mà vẫn sử dụng được Session Manager. Chúng ta sẽ đi qua cách làm này ở phần private instance dưới đây.\nBạn có thể terminate để kết thúc session đang kết nối trước khi qua bước tiếp theo.\n"}, {"uri": "//localhost:1313/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.1-enablevpcdns/", "title": "Kích hoạt DNS hostnames", "tags": [], "description": "", "content": "<PERSON><PERSON><PERSON> hoạt tính năng DNS hostnames trên VPC. <PERSON><PERSON> tạo được VPC Endpoint chúng ta sẽ cần bật tính năng DNS hostnames trên VPC. Truy cập đến giao diện quản trị của dịch vụ VPC\nClick Your VPCs.\nChọn Lab VPC.\nClick Actions.\nClick Edit DNS hostnames.\nClick Endpoint, sau đó click Create Endpoint.\nTại trang Edit DNS hostnames. Click chọn Enable. Click Save changes. "}, {"uri": "//localhost:1313/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.2-createvpcendpoint/*******-endpointssm/", "title": "Tạo Endpoint ssm", "tags": [], "description": "", "content": "Tạo VPC Endpoint SSM Truy cập đến giao diện quản trị của dịch vụ VPC Click Endpoints. Click Create endpoint. Tại trang Create endpoint. Tại mục Name tag điền SSM. Tại mục Service Category chọn AWS Services. Tại mục Service Name, Tại mục Service category chọn: AWS services Tại mục Service Name nhập: SSM sau đó chọn Service Name: com.amazonaws.ap-southeast-1.ssm. Tại cột Service Name, click chọn com.amazonaws.ap-southeast-1.ssm. Tại mục VPC, chọn Lab VPC. Chọn AZ đầu tiên, sau đó chọn subnet Lab Private Subnet. Kéo chuột xuống dưới. Tại mục Security Group, chọn Security group SG VPC Endpoint mà chúng ta đã tạo trước đó. Tại mục Policy, chọn Full access Kéo chuột xuống dưới cùng. Click Create endpoint. Chúng ta đã tạo xong VPC Interface Endpoint cho SSM. "}, {"uri": "//localhost:1313/vi/", "title": "Website thư<PERSON><PERSON> mại điện tử", "tags": [], "description": "", "content": "Xây dựng website thương mại điện tử động với AWS Serverless Tổng quan Trong Workshop này, bạn sẽ triển khai một website thương mại điện tử động sử dụng framework Hugo trên nền tảng điện toán đám mây AWS. Bạn sẽ học cách chuẩn bị môi trường, cấu hình tài khoản AWS, xây dựng website với Hugo, và triển khai toàn bộ hệ thống trên các dịch vụ chính của AWS như API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, đồng thời sử dụng SAM CLI để tự động hoá quá trình triển khai.\nMục tiêu của Workshop Hiểu và thao tác thành thạo các công cụ AWS cần thiết cho một dự án website động. Bi<PERSON>t cách chuẩn bị, c<PERSON><PERSON> đặt, cấu hình môi trường phát triển cho dự án Hugo. <PERSON><PERSON><PERSON> dự<PERSON>, đóng gói và triển khai website động với Hugo kết hợp các dịch vụ serverless hiện đại của AWS. Thiết kế và triển khai (deploy) với API Gateway, xử lý logic động bằng Lambda, lưu trữ dữ liệu trên DynamoDB, và quản lý website động trên S3. Sử dụng CloudFormation để tự động tạo và cấu hình tài nguyên AWS, theo dõi và giám sát hoạt động hệ thống với CloudWatch. Cấu hình tên miền và phân giải DNS với Route 53 để truy cập website động qua internet. Ứng dụng quy trình DevOps tự động hóa triển khai và vận hành website trên nền tảng AWS một cách hiệu quả. Kiến thức thu được sau Workshop Sau khi hoàn thành Workshop này, bạn sẽ:\nHiểu rõ kiến trúc và quy trình triển khai một website thương mại điện tử động trên nền tảng AWS. Học được cách sử dụng các dịch vụ AWS như API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch và SAM CLI trong một dự án thực tế. Biết cách xây dựng, đóng gói và triển khai website động với Hugo, kết nối frontend với backend qua API Gateway và Lambda. Thực hành quản lý dữ liệu động với DynamoDB, quản lý hạ tầng tự động bằng CloudFormation. Cấu hình tên miền với Route 53, giám sát hệ thống và log ứng dụng qua CloudWatch. Sẵn sàng áp dụng kiến thức vào các dự án thực tế, các bài toán về website động, serverless, hoặc DevOps trên AWS. Nội dung Giới thiệu Các bước chuẩn bị Triển khai backend Kiểm thử API backend với Postman Triển khai frontend Cấu hình tên miền &amp; theo dõi hệ thống Các câu hỏi thường gặp Dọn dẹp tài nguyên "}, {"uri": "//localhost:1313/vi/2-prerequiste/2.2-installnodejs/", "title": "Cài đặt ssss", "tags": [], "description": "", "content": "<PERSON><PERSON>i đặt <PERSON> là một static site generator n<PERSON>h chóng, mạnh mẽ và dễ sử dụng. Trong bước nà<PERSON>, bạn sẽ cài đặt <PERSON> để xây dựng và triển khai giao diện cho website thương mại điện tử động của mình.\nBước 1: <PERSON><PERSON><PERSON> đặt Hugo Tải và cài đặt Hugo:\nT<PERSON>y cập vào trang chính của Hugo tại: <PERSON> Releases. Chọn phiên bản phù hợp với hệ điều hành của bạn (Windows, macOS, hoặc Linux). Tải file .zip hoặc .tar.gz về và giải nén vào thư mục bạn chọn. Cài đặt Hugo trên Windows:\nTải bản .zip của Hugo từ trang Hugo for Windows. Giải nén và di chuyển tệp hugo.exe vào thư mục mà bạn dễ dàng truy cập (ví dụ: C:\\Hugo). Thêm đường dẫn thư mục vào PATH: Vào Control Panel → System and Security → System → Advanced system settings. Nhấn Environment Variables → Chọn Path và nhấn Edit. Thêm thư mục chứa hugo.exe vào. Kiểm tra cài đặt:\nMở Command Prompt và nhập lệnh sau: hugo version Nếu mọi thứ cài đặt thành công, bạn sẽ thấy thông tin phiên bản Hugo hiện ra. Cài đặt Hugo trên macOS:\nCài đặt qua Homebrew: brew install hugo Cài đặt Hugo trên Linux (Ubuntu):\nSử dụng lệnh apt: sudo apt-get install hugo Hugo đã được cài đặt thành công! Bạn có thể bắt đầu tạo website của mình với theme đã chọn.\nBước 2: Tạo Project Hugo Mới Sau khi cài đặt thành công Hugo, bạn có thể tạo một project Hugo mới với các bước sau:\nTạo thư mục cho project:\nMở terminal (hoặc Command Prompt trên Windows) và di chuyển đến thư mục bạn muốn tạo project. Chạy lệnh sau để tạo project Hugo mới: hugo new site my-ecommerce-website Lệnh này sẽ tạo một thư mục my-ecommerce-website chứa các file cấu hình và thư mục cần thiết cho dự án. Chọn và cài đặt theme:\nTruy cập trang Hugo Themes và chọn một theme mà bạn thích. Cài đặt theme bằng cách clone repository của theme vào thư mục themes trong project của bạn: cd my-ecommerce-website git init git submodule add https://github.com/gohugoio/hugo-theme-ananke.git themes/ananke Chạy thử website:\nSau khi cài đặt theme, bạn có thể chạy Hugo để xem website của mình trên máy local bằng cách sử dụng lệnh sau: hugo server Truy cập vào http://localhost:1313 để xem website. Các bước tiếp theo Sau khi cài đặt và chạy Hugo thành công, bạn có thể bắt đầu chỉnh sửa nội dung, cấu hình các trang và thêm các tính năng cho website thương mại điện tử của mình.\nLưu ý quan trọng: Đảm bảo rằng bạn đã cài đặt Hugo đúng cách trên hệ thống của mình. Nếu có vấn đề, hãy kiểm tra lại đường dẫn và cài đặt. Hugo là một công cụ mạnh mẽ cho việc xây dựng các website tĩnh, nhưng trong dự án này, bạn sẽ kết hợp nó với các dịch vụ AWS để triển khai website động. Hãy tiếp tục với các bước tiếp theo để cài đặt các công cụ cần thiết cho frontend và backend.\n"}, {"uri": "//localhost:1313/vi/2-prerequiste/", "title": "<PERSON><PERSON><PERSON> bị môi trường", "tags": [], "description": "", "content": "Trước khi bắt đầu triển khai website thương mại điện tử động sử dụng các dịch vụ AWS như API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch và SAM CLI, bạn cần hoàn thành một số bước chuẩn bị môi trường cơ bản. Những công cụ và dịch vụ này sẽ giúp bạn triển khai ứng dụng một cách nhanh chóng, an toàn và hiệu quả.\nTrong phần này, bạn sẽ thực hiện các bước cài đặt và cấu hình môi trường phát triển cho cả frontend và backend của dự án:\nCài đặt Hugo: Để tạo và triển khai frontend cho website. Cài đặt NodeJS và Yarn: Để hỗ trợ việc xây dựng và quản lý frontend. Cài đặt SAM CLI: Đ<PERSON> triển khai backend serverless trên AWS. Tạo tài khoản AWS và cấu hình IAM: Để thiết lập quyền truy cập và bảo mật cho dự án trên AWS. Các bước chuẩn bị này sẽ đảm bảo rằng bạn có môi trường đầy đủ và hoạt động chính xác trước khi tiến hành triển khai các dịch vụ AWS cho dự án của mình.\n⚠️ Lưu ý: Hãy chắc chắn rằng bạn đã cài đặt đầy đủ các công cụ trước khi bắt đầu làm việc với các dịch vụ AWS. Nếu không, bạn có thể gặp phải sự cố trong quá trình triển khai.\nNội dung Cài đặt Hugo Cài đặt NodeJS Cài đặt Yarn cho frontend Cài đặt SAM CLI cho backend Tạo tài khoản &amp; cấu hình IAM "}, {"uri": "//localhost:1313/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.2-createvpcendpoint/*******-endpointssmmessages/", "title": "Tạo Endpoint ssmmessages", "tags": [], "description": "", "content": "Tạo VPC Endpoint SSMMESSAGES Truy cập đến giao diện quản trị của dịch vụ VPC Click Endpoints. Click Create endpoint. Tại trang Create endpoint. Tại mục Name tag điền SSMMESSAGES. Tại mục Service Category chọn AWS Services. Tại mục Service Name, Tại mục Service category chọn: AWS services Tại mục Service Name nhập: ssmmessages sau đó chọn Service Name: com.amazonaws.ap-southeast-1.ssmmessages. Tại cột Service Name, click chọn com.amazonaws.ap-southeast-1.ssmmessages. Tại mục VPC, chọn Lab VPC. Chọn AZ đầu tiên, sau đó chọn subnet Lab Private Subnet. Kéo chuột xuống dưới. Tại mục Security Group, chọn Security group SG VPC Endpoint mà chúng ta đã tạo trước đó. Tại mục Policy, chọn Full access Kéo chuột xuống dưới cùng. Click Create endpoint. Chúng ta đã tạo xong VPC Interface Endpoint SSMMESSAGES. "}, {"uri": "//localhost:1313/vi/3-accessibilitytoinstances/3.2-private-instance/", "title": "Tạo kết nối đến máy chủ EC2 Private", "tags": [], "description": "", "content": "Đối với Windows instance nằm trong private subnet, không có public IP, không có internet gateway nên không thể đi ra ngoài internet.\nVới loại instance này, cách làm truyền thống là ta sẽ sử dụng kỹ thuật Bastion host tốn nhiều chi phí và công sức, nhưng ở đây chúng ta sẽ sử dụng Session Manager với loại này.\nCơ bản là private instance vẫn phải mở cổng TCP 443 tới System Manager, nhưng không cho kết nối đó đi ra ngoài internet mà chỉ cho đi trong chính VPC của mình, nên đảm bảo được vấn đề bảo mật.\nĐể làm được điều đó, ta phải đưa endpoint của System Manager vào trong VPC, nghĩa là sử dụng VPC interface endpoint:\nVPC interface endpoint được gắn với subnet nên cách làm này không những với private subnet mà còn có thể làm với public subnet, nghĩa là với public subnet, bạn hoàn toàn có thể không cho TCP 443 đi ra ngoài internet.\nNội dung: Kích hoạt DNS hostnames Tạo VPC Endpoint Kết nối Private Instance "}, {"uri": "//localhost:1313/vi/4-s3log/4.2-creates3bucket/", "title": "Tạo S3 Bucket", "tags": [], "description": "", "content": "Trong bước này, chúng ta sẽ tạo 1 S3 bucket để lưu trữ các session logs đư<PERSON>c gửi từ các EC2 instance.\nTạo S3 Bucket Truy cập giao diện quản trị dịch vụ S3 Click Create bucket. Tại trang Create bucket. Tại mục Bucket name điền tên bucket lab-yourname-bucket-0001 Tại mục Region chọn Region bạn đang làm lab hiện tại. Tên S3 bucket phải đảm bảo không trùng với toàn bộ S3 bucket khác trong hệ thống. Bạn có thể thay thế tên mình và điền số ngẫu nhiên khi tạo tên S3 bucket.\nKéo chuột xuống phía dưới và click Create bucket. Khi tạo S3 bucket chúng ta đã thực hiện Block all public access nên các EC2 instance của chúng ta sẽ không thể kết nối tới S3 thông qua mạng internet. Trong bước tiếp theo chúng ta sẽ cấu hình tính năng S3 Gateway Endpoint để cho phép các EC2 instance có thể kết nối tới S3 bucket thông qua mạng nội bộ của VPC.\n"}, {"uri": "//localhost:1313/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.2-createvpcendpoint/", "title": "Tạo VPC Endpoint", "tags": [], "description": "", "content": "Tạo VPC Endpoint SSM Chúng ta sẽ tạo 3 interface endpoint yê<PERSON> c<PERSON> bởi Session Manager: Interface endpoint: com.amazonaws.region.ssm com.amazonaws.region.ec2messages com.amazonaws.region.ssmmessages Bạn có thể tham khảo thêm tại đây\nNội dung: Tạo Endpoint ssm Tạo Endpoint ssmmessages Tạo Endpoint ec2messages "}, {"uri": "//localhost:1313/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.3-connectec2/", "title": "Kết nối EC2 Private", "tags": [], "description": "", "content": "Gán IAM role và restart EC2 instance. T<PERSON>y cập giao diện quản trị dịch vụ EC2 Click chọn Private Windows Instance. Click Actions. Click Security. Click Modify IAM Role. Tại trang Modify IAM Role. T<PERSON><PERSON> mục IAM role, chọn SSM-Role. Click Save. Click chọn Private Windows Instance. Click Instance state. Click Reboot instance để thực hiện restart, sau đó click Reboot để xác nhận. Bạn hãy đợi 5 phút trước khi làm bước tiếp theo nhé.\nKết nối tới máy chủ private EC2 instance. Truy cập vào giao diện quản trị dịch vụ System Manager - Session Manager Click Start session. Click chọn Private Windows Instance. Click Start session. G<PERSON> lệnh ipconfig để kiểm tra thông tin địa chỉ IP của Private Windows Instance như dưới đây. "}, {"uri": "//localhost:1313/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.2-createvpcendpoint/*******-endpointec2messages/", "title": "Tạo Endpoint ec2messages", "tags": [], "description": "", "content": "Tạo VPC Endpoint EC2MESSAGES Truy cập đến giao diện quản trị của dịch vụ VPC Click Endpoints. Click Create endpoint. Tại trang Create endpoint. Tại mục Name tag điền SSMMESSAGES. Tại mục Service Category chọn AWS Services. Tại mục Service Name, Tại mục Service category chọn: AWS services Tại mục Service Name nhập: ec2 sau đó chọn Service Name: com.amazonaws.ap-southeast-1.ec2messages. Tại cột Service Name, click chọn com.amazonaws.ap-southeast-1.ec2messages. Tại mục VPC, chọn Lab VPC. Chọn AZ đầu tiên, sau đó chọn subnet Lab Private Subnet. Kéo chuột xuống dưới. Tại mục Security Group, chọn Security group SG VPC Endpoint mà chúng ta đã tạo trước đó. Tại mục Policy, chọn Full access Kéo chuột xuống dưới cùng. Click Create endpoint. Chúng ta đã tạo xong VPC Interface Endpoint EC2MESSAGES.\nĐảm bảo 3 endpoint cần thiết đã được tạo như hình dưới.\n"}, {"uri": "//localhost:1313/vi/3-accessibilitytoinstances/", "title": "Tạo kết nối đến máy chủ EC2", "tags": [], "description": "", "content": "Trong bước này, chúng ta sẽ thực hiện tạo kết nối đến các máy chủ EC2 của chúng ta, nằm trong cả public và private subnet.\nNội dung 3.1. Tạo Kết nối đến máy chủ EC2 Public 3.2. Tạo Kết nối đến máy chủ EC2 Private\n"}, {"uri": "//localhost:1313/vi/4-s3log/4.3-creategwes3/", "title": "Tạo S3 Gateway endpoint", "tags": [], "description": "", "content": " <PERSON><PERSON><PERSON> cập và<PERSON> giao diện quản trị dịch vụ VPC Click Endpoints. Click Create endpoint. Tại trang Create endpoint. Tạ<PERSON> mục Name tag điền S3GW. Tại mục Service Category click chọn AWS services. <PERSON><PERSON><PERSON> ô tìm kiếm điền S3, sau đ<PERSON> chọn com.amazonaws.[region].s3 Tại mục Services chọn com.amazonaws.[region].s3 có Type là Gateway. Tại mục VPC , chọn Lab VPC. Tại mục Route tables, chọn cả 2 route table. Kéo chuột xuống dưới cùng, click Create endpoint. Bước tiếp theo chúng ta sẽ tiến hành cấu hình Session Manager để có thể lưu trữ các session logs tới S3 bucket chúng ta đã tạo.\n"}, {"uri": "//localhost:1313/vi/4-s3log/", "title": "<PERSON><PERSON><PERSON><PERSON> session logs", "tags": [], "description": "", "content": "Với Session Manager chún<PERSON> ta có thể xem đượ<PERSON> lịch sử các kết nối tới các instance thông qua Session history. <PERSON><PERSON> <PERSON>hiên chúng ta chưa xem được chi tiết các câu lệnh được sử dụng.\nTrong phần này chúng ta sẽ tiến hành tạo S3 bucket và thực hiện cấu hình lưu trữ các session logs để xem được chi tiết các câu lệnh được sử dụng trong session.\nNội dung: Cập nhật IAM Role Tạo S3 Bucket Tạo S3 Gateway endpoint Cấu hình Session logs "}, {"uri": "//localhost:1313/vi/4-s3log/4.4-configsessionlogs/", "title": "<PERSON> session logs", "tags": [], "description": "", "content": "Theo dõi session logs T<PERSON><PERSON> cập giao diện quản trị dịch vụ System Manager - Session Manager Click tab Preferences. Click Edit. Kéo chuột xuống phía dưới, tại mục S3 logging, click chọn Enable. Bỏ chọn Allow only encrypted S3 buckets. Click chọn Choose a bucket name from the list. Chọn S3 bucket bạn đã tạo. Kéo chuột xuống phía dưới, click Save để lưu cấu hình.\nTruy cập giao diện quản trị dịch vụ System Manager - Session Manager\nClick Start session. Click chọn Private Windows Instance. Click Start session. Gõ lệnh ipconfig. Gõ lệnh hostname. Click Terminate để thoát session, click Terminate 1 lần nữa để xác nhận. Kiểm tra Session logs trong S3 Truy cập vào giao diện quản trị dịch vụ S3 Click vào tên S3 bucket chúng ta đã tạo cho bài lab. Click vào tên file sessions log Tại trang chi tiết objects , click Open. File logs sẽ được mở ở 1 tab mới trên trình duyệt.Bạn có thể xem các câu lệnh đã được lưu trữ lại trong session logs. "}, {"uri": "//localhost:1313/vi/5-portfwd/", "title": "Port Forwarding", "tags": [], "description": "", "content": "\rPort Forwarding là mốt cách thức hữu ích để chuyển hướng lưu lượng mạng từ 1 địa chỉ IP - Port này sang 1 địa chỉ IP - Port khác. Với Port Forwarding chúng ta có thể truy cập một EC2 instance nằm trong private subnet từ máy trạm của chúng ta.\nChúng ta sẽ cấu hình Port Forwarding cho kết nối RDP giữa máy của mình với Private Windows Instance nằm trong private subnet mà chúng ta đã tạo cho bài thực hành này.\nTạo IAM User có quyền kết nối SSM Truy cập vào giao diện quản trị dịch vụ IAM Click Users , sau đó click Add users. Tại trang Add user. Tại mục User name, điền Portfwd. Click chọn Access key - Programmatic access. Click Next: Permissions. Click Attach existing policies directly. Tạ<PERSON> ô tìm kiếm , điền ssm. Click chọn AmazonSSMFullAccess. Click Next: Tags, click Next: Reviews. Click Create user. <PERSON><PERSON><PERSON> lại thông tin Access key ID và Secret access key để thực hiện cấu hình AWS CLI. Cài đặt và cấu hình AWS CLI và Session Manager Plugin Để thực hiện phần thực hành này, đảm bảo máy trạm của bạn đã cài AWS CLI và Session Manager Plugin\nBạn có thể tham khảo thêm bài thực hành về cài đặt và cấu hình AWS CLI tại đây.\nVới Windows thì khi giải nén thư mục cài đặt Session Manager Plugin bạn hãy chạy file install.bat với quyền Administrator để thực hiện cài đặt.\nThực hiện Portforwarding Chạy command dưới đây trong Command Prompt trên máy của bạn để cấu hình Port Forwarding. aws ssm start-session --target (your ID windows instance) --document-name AWS-StartPortForwardingSession --parameters portNumber=&#34;3389&#34;,localPortNumber=&#34;9999&#34; --region (your region) Thông tin Instance ID của Windows Private Instance có thể tìm được khi bạn xem chi tiết máy chủ EC2 Windows Private Instance.\nCâu lệnh ví dụ C:\\Windows\\system32&gt;aws ssm start-session --target i-06343d7377486760c --document-name AWS-StartPortForwardingSession --parameters portNumber=&#34;3389&#34;,localPortNumber=&#34;9999&#34; --region ap-southeast-1 Nếu câu lệnh của bạn báo lỗi như dưới đây : SessionManagerPlugin is not found. Please refer to SessionManager Documentation here: http://docs.aws.amazon.com/console/systems-manager/session-manager-plugin-not-found\nChứng tỏ bạn chưa cài Session Manager Plugin thành công. Bạn có thể cần khởi chạy lại Command Prompt sau khi cài Session Manager Plugin.\nKết nối tới Private Windows Instance bạn đã tạo bằng công cụ Remote Desktop trên máy trạm của bạn. Tại mục Computer: điền localhost:9999. Quay trở lại giao diện quản trị của dịch vụ System Manager - Session Manager. Click tab Session history. Chúng ta sẽ thấy các session logs với tên Document là AWS-StartPortForwardingSession. Chúc mừng bạn đã hoàn tất bài thực hành hướng dẫn cách sử dụng Session Manager để kết nối cũng như lưu trữ các session logs trong S3 bucket. Hãy nhớ thực hiện bước dọn dẹp tài nguyên để tránh sinh chi phí ngoài ý muốn nhé.\n"}, {"uri": "//localhost:1313/vi/6-cleanup/", "title": "D<PERSON>n dẹp tài nguyên  ", "tags": [], "description": "", "content": "Chúng ta sẽ tiến hành các bước sau để xóa các tài nguyên chúng ta đã tạo trong bài thực hành này.\nXóa EC2 instance Truy cập giao diện quản trị dịch vụ EC2 Click Instances. Click chọn cả 2 instance Public Linux Instance và Private Windows Instance. Click Instance state. Click Terminate instance, sau đó click Terminate để xác nhận. Truy cập giao diện quản trị dịch vụ IAM Click Roles. T<PERSON><PERSON> ô tìm kiếm , điền SSM. Click chọn SSM-Role. Click Delete, sau đó điền tên role SSM-Role và click Delete để xóa role. Click Users. Click chọn user Portfwd. Click Delete, sau đó điền tên user Portfwd và click Delete để xóa user. Xóa S3 bucket Truy cập giao diện quản trị dịch vụ System Manager - Session Manager. Click tab Preferences. Click Edit. Kéo chuột xuống dưới. Tại mục S3 logging. Bỏ chọn Enable để tắt tính năng logging. Kéo chuột xuống dưới. Click Save. Truy cập giao diện quản trị dịch vụ S3 Click chọn S3 bucket chúng ta đã tạo cho bài thực hành. ( Ví dụ : lab-fcj-bucket-0001 ) Click Empty. Điền permanently delete, sau đó click Empty để tiến hành xóa object trong bucket. Click Exit. Sau khi xóa hết object trong bucket, click Delete Điền tên S3 bucket, sau đó click Delete bucket để tiến hành xóa S3 bucket. Xóa các VPC Endpoint Truy cập vào giao diện quản trị dịch vụ VPC Click Endpoints. Chọn 4 endpoints chúng ta đã tạo cho bài thực hành bao gồm SSM, SSMMESSAGES, EC2MESSAGES, S3GW. Click Actions. Click Delete VPC endpoints. Tại ô confirm , điền delete. Click Delete để tiến hành xóa các endpoints. Click biểu tượng refresh, kiểm tra tất cả các endpoints đã bị xóa trước khi làm bước tiếp theo. Xóa VPC Truy cập vào giao diện quản trị dịch vụ VPC Click Your VPCs. Click chọn Lab VPC. Click Actions. Click Delete VPC. Tại ô confirm, điền delete để xác nhận, click Delete để thực hiện xóa Lab VPC và các tài nguyên liên quan. "}, {"uri": "//localhost:1313/vi/categories/", "title": "Categories", "tags": [], "description": "", "content": ""}, {"uri": "//localhost:1313/vi/tags/", "title": "Tags", "tags": [], "description": "", "content": ""}]