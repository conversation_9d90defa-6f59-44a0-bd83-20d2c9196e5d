---
title : "Giới thiệu"
date : 2023-10-25 
weight : 1 
chapter : false
pre : " <b> 1. </b> "
---

### Giới thiệu hệ thống
Workshop này sẽ hướng dẫn bạn xây dựng và triển khai một **website thương mại điện tử động** sử dụng các dịch vụ serverless hiện đại của AWS. Bạn sẽ sử dụng các thành phần chủ chốt như API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch và SAM CLI để tự động hoá, mở rộng và tối ưu chi phí cho hệ thống.


### Kiến trúc tổng thể
Dưới đây là kiến trúc tổng thể của hệ thống.  
*(Ch<PERSON><PERSON> sơ đồ kiến trúc tại đây)*
- **Frontend:** Sử dụng theme Hugo, lưu trữ tĩnh trên Amazon S3 và phân phối qua CloudFront.
- **Backend:** Giao tiếp qua RESTful API với API Gateway, xử lý nghiệp vụ bởi AWS Lambda.
- **Cơ sở dữ liệu:** Sử dụng DynamoDB để lưu trữ dữ liệu động, mở rộng dễ dàng.
- **Quản lý hạ tầng:** CloudFormation tự động tạo & cấu hình tài nguyên AWS. Sử dụng SAM CLI để triển khai và tự động hóa serverless.
- **Tên miền & Quản lý hoạt động:** Rooute 53 quản lý tên miền/DNS. CloudWatch dùng để theo dõi, ghi nhận log và cảnh báo cho toàn bộ hệ thống.


### Sơ đồ luồng dữ liệu
Sơ đồ sau minh họa luồng dữ liệu chính và tương tác giữa các thành phần hệ thống.  
*(Chèn sơ đồ luồng dữ liệu hoặc sequence diagram tại đây)*


### Danh sách dịch vụ AWS sử dụng
- **API Gateway:** Tạo REST API cho giao tiếp giữa frontend-backend.
- **S3:** Lưu trữ website tĩnh và các tài nguyên của website.
- **Lambda:** Xử lý logic backend serverless.
- **DynamoDB:** Co sở dữ liệu.
- **CloudFormation:** Quản lý hạ tầng tự động.
- **Route 53:** Đăng ký tên miền và DNS.
- **CloudWatch:** Giám sát hệ thống và cảnh báo, giúp debugging/tracking hiệu quả.
- **SAM CLI:** Tự động hóa triển khai tài nguyên serverless.


### Tính năng chính của website
- Kiến trúc serverless hoàn toàn trên AWS.
- Triển khai website động, bảo mật, mở rộng tự động.
- Giao diện thân thiện với Hugo.
- Backend động sử dụng API RESTful qua API Gateway & Lambda.
- Cơ sở dữ liệu động với DynamoDB.
- Xử lý dữ liệu thời gian thực bằng Lambda & DynamoDB.
- Tích hợp giám sát, logging và cấu hình DNS.
- Hạ tầng dưới dạng mã với CloudFormation & SAM CLI.