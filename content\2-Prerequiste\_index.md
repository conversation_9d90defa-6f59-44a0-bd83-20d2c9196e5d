---
title : "Preparation"
date : 2023-10-25
weight : 2
chapter : false
pre : " <b> 2 </b> "
---

### Overview of "Preparation"

Before you begin deploying your dynamic e-commerce website using AWS services like **API Gateway**, **S3**, **Lambda**, **CloudFormation**, **DynamoDB**, **Route 53**, **CloudWatch**, and **SAM CLI**, you need to complete a few basic environment preparation steps. These tools and services will help you quickly, securely, and efficiently deploy your application.

In this section, you will perform the necessary setup steps for both the frontend and backend environments of the project:

- **Install Hugo**: To build and deploy the frontend of the website.
- **Install NodeJS and Yarn**: To support frontend build and management.
- **Install SAM CLI**: To deploy the serverless backend on AWS.
- **Create AWS account and configure IAM**: To set up access and security for the AWS environment.

These preparation steps will ensure that you have the proper development environment in place and working correctly before proceeding with deploying AWS services for your project.

⚠️ **Note**: Make sure you have all the tools installed before starting to work with AWS services. Otherwise, you may encounter issues during deployment.

### Content
- [Install NodeJS](2.1-installNodejs/)
- [Install Yarn for frontend](2.2-installYarn/)
- [Install SAM CLI for backend](2.3-installSamcli/)
- [Create AWS Account & Configure IAM](2.4-createIam/)
