---
title: "Introduction"
date: 2023-10-25
weight: 1
chapter: false
pre: " <b> 1. </b> "
---

### System Introduction
This workshop will guide you to build and deploy a **dynamic e-commerce website** using modern AWS serverless services. You will use key components such as API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, and SAM CLI to automate, scale, and optimize the cost of your system.

### Overall Architecture
Below is the overall architecture of the system.  
*(Insert architecture diagram here)*
- **Frontend:** Uses Hugo theme, hosted statically on Amazon S3 and delivered through CloudFront.
- **Backend:** Communicates via RESTful APIs with API Gateway, business logic is handled by AWS Lambda.
- **Database:** Uses DynamoDB for dynamic data storage, easily scalable.
- **Infrastructure Management:** CloudFormation automatically creates & configures AWS resources. SAM CLI is used for serverless deployment and automation.
- **Domain & Operation Management:** Route 53 manages domain/DNS. CloudWatch is used to monitor, log, and provide alerts for the whole system.

### Data Flow Diagram
The following diagram illustrates the main data flow and interactions between system components.  
*(Insert data flow or sequence diagram here)*

### List of AWS Services Used
- **API Gateway:** Creates REST APIs for communication between frontend and backend.
- **S3:** Stores the static website and website assets.
- **Lambda:** Handles serverless backend logic.
- **DynamoDB:** The database.
- **CloudFormation:** Automated infrastructure management.
- **Route 53:** Domain registration and DNS management.
- **CloudWatch:** System monitoring and alerting, supports effective debugging/tracking.
- **SAM CLI:** Automates the deployment of serverless resources.

### Main Features of the Website
- Fully serverless architecture on AWS.
- Deploy a dynamic, secure, and automatically scalable website.
- User-friendly interface with Hugo.
- Dynamic backend using RESTful APIs via API Gateway & Lambda.
- Dynamic database with DynamoDB.
- Real-time data processing with Lambda & DynamoDB.
- Integrated monitoring, logging, and DNS configuration.
- Infrastructure as code with CloudFormation & SAM CLI.
