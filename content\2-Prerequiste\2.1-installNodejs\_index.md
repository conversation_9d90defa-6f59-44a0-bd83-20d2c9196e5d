---
title : "Install NodeJS"
date : 2023-10-25
weight : 3
chapter : false
pre : " <b> 2.1 </b> "
---

NodeJS is a powerful and popular JavaScript runtime environment used for developing dynamic web applications. In this step, you will install NodeJS on your operating system to develop your dynamic e-commerce website.

#### Step 1: Install NodeJS

1. **Download NodeJS from the official site:**
   - Visit the official Node.js website at [Node.js](https://nodejs.org/) and download the LTS (Long Term Support) version for your operating system (Windows, macOS, or Linux).
   - Download the **.msi** for Windows, **.pkg** for macOS, or **.tar.xz** for Linux.

2. **Install NodeJS on Windows:**
   - After downloading the **.msi** file, open it and follow the installation steps.
   - Ensure that you check the option to **Add to PATH** during installation.

3. **Install NodeJS on macOS:**
   - Install using **Homebrew**:
     ```bash
     brew install node
     ```

4. **Install NodeJS on Linux (Ubuntu):**
   - Use the following command to install NodeJS:
     ```bash
     sudo apt install nodejs
     ```

5. **Verify Installation:**
   - After installation, open the terminal (or Command Prompt on Windows) and run the following command to check the NodeJS version:
     ```bash
     node -v
     ```
   - If the installation was successful, the version of NodeJS will be displayed.

{{% notice success %}}
NodeJS has been successfully installed! You can now start building your JavaScript-based applications for both the frontend and backend of your website.
{{% /notice %}}

#### Step 2: Install npm

1. **npm (Node Package Manager)** is a tool for managing libraries and packages in your NodeJS project.

2. **Verify npm:**
   - npm is installed automatically when you install NodeJS.
   - Verify the npm version with the command:
     ```bash
     npm -v
     ```

3. **Update npm (if needed):**
   - If you want to update npm to the latest version, run:
     ```bash
     npm install -g npm
     ```

### Next Steps
After installing NodeJS and npm, you can proceed to install the necessary libraries and packages for your project.

{{% notice info %}}
Next, you can install Yarn to help manage frontend libraries more easily.
{{% /notice %}}
