---
title : "Deploying the Frontend"
date : 2023-10-25
weight : 5
chapter : false
pre : " <b> 5 </b> "
---

### Deploying the Frontend

After the backend is ready and has been successfully tested, the next step is to **deploy the frontend of your website to AWS S3** to create a static website, optimizing both costs and page loading speed.  
At the same time, you will configure hosting, caching, and CORS so that the frontend operates smoothly and securely when connecting to the backend API.

**Objectives of this section:**
- Guide you through uploading and deploying frontend code to an AWS S3 Bucket.
- Enable static website hosting, and configure cache and CORS for stable and secure frontend access.
- Connect the frontend to the tested backend API endpoint from previous steps.

{{% notice info %}}
**Note:**  
Make sure you have **yarn** installed in your project directory.
{{% /notice %}}

**By the end of this section, you will:**
- Have successfully deployed the frontend website on S3, making it publicly accessible to everyone.
- Master hosting, caching, and CORS configuration—ensuring your website loads quickly, safely, and is compatible with the backend.
- Be ready to upgrade the system with a custom domain, CloudFront, or new features in the next steps.

### Contents
- [Deploy Frontend to S3 Bucket](5.1-frontend-s3/)
- [Enable static hosting, configure CORS](5.2-enable-static-hosting/)
- [Configure Google OAuth2 Client ID and Client Secret](5.3-clientid-clientserver/)
- [Connect the frontend to the backend API](5.4-connect-frontend-api-backend/)
