---
title : "Triển khai frontend lên S3 Bucket"
date : 2023-10-25
weight : 6
chapter : false
pre : " <b> 5.1 </b> "
---

### Triển khai frontend lên S3 Bucket

Để website frontend hoạt động online, bạn sẽ upload toàn bộ mã nguồn đã build lên **AWS S3 Bucket**. S3 sẽ đóng vai trò là hosting tĩnh, giúp phân phối website với chi phí thấp và tốc độ nhanh.

**C<PERSON>c bước thực hiện:**

1. **Build mã nguồn frontend**

   - Mở `termiral` trên Vscode và truy cập vào thư mục `frontend`.
   - Copy lệnh sau và dán vào termiral
     ```
     yarn build
     ```
   - <PERSON>h<PERSON> mục `build` sẽ chứa tất cả file tĩnh cần upload.
   - Bạn đợi khoảng 1 phút để build dự án

![yarn build](/images/yarn_build.png)


2. **Tạo S3 Bucket mới trên AWS**

   - <PERSON><PERSON><PERSON> cập [AWS S3 Console](https://s3.console.aws.amazon.com/s3).
   - Click **Create bucket**.
   - Đặt tên bucket, ví dụ: `my-frontend-bucket-2025`.
   - **Region:** Chọn cùng region với backend (nên là ap-southeast-1 cho đồng bộ).
   - **Tắt “Block all public access”**.
   - Tích chọn vào **I acknowledge that the current settings might result in this bucket and the objects within becoming public.** , xác nhận cảnh báo để website có thể public
   - Nhấn **Create bucket** để hoàn tất.

![bucket_my_frontend](/images/create_bucket_my_frontend_s3.png)

{{% notice warning %}}
**Cẩn thận:**  
Bucket chứa website tĩnh cần mở quyền public read cho tất cả mọi người. Tuy nhiên không nên upload dữ liệu nhạy cảm vào đây vì bất cứ ai có link đều truy cập được!
{{% /notice %}}

3. **Cấp quyền public read cho bucket**

   - Sau khi Bucket đã được tạo thành công, chúng ta sẽ tiến hành cấp quyền cho Bucket.
   - Click vào Bucket vừa tạo

![click_bucket_myfrontend](/images/click_bucket_myfrontend.png)

   - Truy cập **Permissions** của bucket 
   - Cuộn xuống dưới tìm mục **Bucket policy**.
   - Chọn **Edit**

![click_edit_bucket_policy](/images/click_edit_bucket_policy.png)

   - Thêm policy cho phép public đọc file, ví dụ:
     ```json
     {
       "Version": "2012-10-17",
       "Statement": [
         {
           "Sid": "PublicReadGetObject",
           "Effect": "Allow",
           "Principal": "*",
           "Action": "s3:GetObject",
           "Resource": "arn:aws:s3:::my-frontend-bucket-2025/*"
         }
       ]
     }
     ```

   - Nhấn Save để áp dụng.

     ![click_edit_bucket_policy](/images/set_permisson_bucket_myfrontend.png)

4. **Upload mã nguồn build lên bucket bằng AWS CLI**

   - Đảm bảo bạn đã cài đặt và cấu hình AWS CLI (đã chạy `aws configure`).
   - Thực hiện lệnh:
        ```
        cd frontend
        aws s3 cp build/ s3://my-frontend-bucket-2025/ --recursive
        ```
        ![build_s3_to_frontend](/images/build_s3_to_frontend.png)

   - Tham số --recursive giúp upload tất cả file và folder con bên trong build.

5. **Kiểm tra lại nội dung bucket**

   - Vào lại bucket trên AWS Console, xác nhận toàn bộ file (index.html, main.js, CSS, ảnh…) đã được upload thành công.
   - Có thể click trực tiếp vào file (VD: `index.html`) và copy URL tại **Object URL** để kiểm tra file đã public chưa (phải xem được file HTML/raw trên trình duyệt).

   - Bạn có thể kiểm tra các file và folder đã upload lên s3 bằng cách truy cập vào Object

        ![build_s3_to_frontend](/images/file_uploaded_s3_after_build.png)

    **Kết luận:**  
    Sau khi hoàn thành các bước trên, bạn đã đưa website frontend của mình lên AWS S3 và sẵn sàng bật static hosting ở bước tiếp theo.

