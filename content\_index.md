---
title: "Dynamic E-Commerce Website"
date: 2023-10-25
weight: 10
chapter: false
---
<!-- # BUILD E-COMMERCE WEBSITE USING API GATERWAY – S3 – LAMBDA – CLOUD FOUMATION – DYNAMODB – ROUTER 53 – CLOUD WATCH – SAM CLI -->
# Building a Dynamic E-Commerce Website with AWS Serverless Services


### Overview
This Workshop will guide you to deploy a **dynamic e-commerce website** using the Hugo framework on the AWS cloud platform. You will learn how to set up your environment, configure your AWS account, build your website with <PERSON>, and deploy the entire system using key AWS services such as API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, and leverage the SAM CLI for automated deployment.

![ConnectPrivate](/images/arc-log.png) 

### Workshop Objectives

- Master essential AWS tools required for a dynamic website project.
- Learn how to prepare, install, and configure the development environment for a Hugo-based project.
- Build, package, and deploy a dynamic website with <PERSON> integrated with modern AWS serverless services.
- Design and deploy with API Gateway, implement backend logic using Lambda, store data in DynamoDB, and manage the dynamic website on S3.
- Use CloudFormation to automate the creation and configuration of AWS resources, monitor and manage the system with CloudWatch.
- Configure domain names and DNS resolution with Route 53 for public access to your dynamic website.
- Apply DevOps practices to automate the deployment and operation of the website on AWS efficiently.

### Workshop Outcomes

After completing this Workshop, you will:
- Clearly understand the architecture and deployment process of a dynamic e-commerce website on AWS.
- Know how to use AWS services such as API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, and SAM CLI in a real-world project.
- Be able to build, package, and deploy a dynamic website with Hugo, connecting the frontend to the backend via API Gateway and Lambda.
- Practice dynamic data management with DynamoDB and automate infrastructure with CloudFormation.
- Configure domain names with Route 53, monitor your system, and log application activity with CloudWatch.
- Be ready to apply this knowledge to real projects, dynamic website problems, serverless, or DevOps tasks on AWS.

### Content
 1. [Introduction ](1-introduce/)
 2. [Preparation](2-prerequiste/)
 3. [Backend Deployment](3-deployBackend/)
 4. [Backend API Testing with Postman](4-testBackendApi/)
 5. [Frontend Deployment](5-deployFrontend/)
 6. [Domain & System Monitoring Configuration](6-domainMonitoring/)
 7. [Frequently Asked Questions](7-faq/)
 8. [Clean up resources](8-cleanup/)
