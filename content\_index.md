---
title : "Session Management"
date : 2023-10-25
weight : 1
chapter : false
---
# Work with Amazon System Manager - Session Manager

### Overall
 In this lab, you'll learn the basics and practice of Amazon  System Manager - Session Manager
. Perform creating public and private instance connections. 

![ConnectPrivate](/images/arc-log.png) 

### Content
 1. [Introduction ](1-introduce/)
 2. [Preparation](2-prerequiste/)
 3. [Backend Deployment](3-accessibilitytoinstances/)
 4. [Test Backend API with Postman](3-accessibilitytoinstances/)
 5. [Frontend Deployment](4-s3log/)
 6. [Configure Domain and Monitor the System](5-Portfwd/)
 7. [FAQ](5-Portfwd/)
 8. [Clean up resources](6-cleanup/)
