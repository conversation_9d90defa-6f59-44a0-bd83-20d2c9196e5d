---
title : "Bật static hosting, cấu hình cors"
date : 2023-10-25
weight : 7
chapter : false
pre : " <b> 5.2 </b> "
---

### Bật static hosting, cấu hình cors

<PERSON><PERSON> <PERSON>hi đã upload mã nguồn frontend lên S3 Bucket, bạn cần **bật tính năng static website hosting**, cấu hình file index, error, thiết lập policy CORS và cache để website chạy ổn định, truy cập được từ trình duyệt và có thể kết nối tới API backend.

**Các bước thực hiện:**

1. **Bật static website hosting cho bucket**

   - Truy cập vào bucket frontend trên [AWS S3 Console](https://s3.console.aws.amazon.com/s3/).
   - Chọn tab **Properties**.
   - Kéo xuống phần **Static website hosting**, nhấn **Edit**.
![static_website_hosting](/images/static_website_hosting.png)

   - Chọn **Enable**.
   - Nhập tên file `index.html` cho trường **Index document**.
   - <PERSON><PERSON> thể nhập thêm `index.html` cho trường **Error document** n.
   - Lưu lại cài đặt.
![static_website_hosting](/images/enable_static_website_hosting.png)

   - AWS sẽ sinh ra một **Website endpoint** dạng:  
     ```
     http://my-frontend-bucket-2025.s3-website-ap-southeast-1.amazonaws.com
     ```
   - Copy URL này để dùng cho bước sau
    ![bucket_website_hosting](/images/bucket_website_hosting.png)

{{% notice info %}}
Bạn phải dùng đúng **Website endpoint** của S3 để truy cập trang web tĩnh, không dùng link Object URL.
{{% /notice %}}

1. **Cấu hình CORS cho bucket**

   - Vào tab **Permissions** → chọn **CORS configuration**.
   - Thêm cấu hình mẫu sau để cho phép frontend gọi API backend trên domain khác:
     ```xml
     <CORSConfiguration>
       <CORSRule>
         <AllowedOrigin>*</AllowedOrigin>
         <AllowedMethod>GET</AllowedMethod>
         <AllowedMethod>HEAD</AllowedMethod>
         <AllowedMethod>PUT</AllowedMethod>
         <AllowedMethod>POST</AllowedMethod>
         <AllowedMethod>DELETE</AllowedMethod>
         <AllowedHeader>*</AllowedHeader>
       </CORSRule>
     </CORSConfiguration>
     ```
   - Nếu muốn giới hạn chỉ cho domain cụ thể (an toàn hơn):
     ```xml
     <CORSConfiguration>
       <CORSRule>
         <AllowedOrigin>https://your-domain.com</AllowedOrigin>
         <AllowedMethod>GET</AllowedMethod>
         <AllowedHeader>*</AllowedHeader>
       </CORSRule>
     </CORSConfiguration>
     ```
   - Nhấn Save.

{{% notice warning %}}
**Lưu ý:**  
Để bảo mật tốt, khi triển khai thật, bạn nên giới hạn trường `<AllowedOrigin>` đúng với domain frontend, không nên để dấu `*` quá rộng!
{{% /notice %}}

3. **Kiểm tra hoạt động website**

   - Dùng link **Website endpoint** truy cập thử website.
   - Nếu index.html tải thành công, website đã hoạt động trên S3.
   - Nếu gặp lỗi 403/404:
     - Kiểm tra lại quyền bucket (bucket policy).
     - Đảm bảo đã bật static hosting.
     - Xem lại tên file index/error đúng chính tả.

**Kết luận:**  
Bật static hosting và cấu hình cache/CORS đúng giúp website frontend trên S3 hoạt động ổn định, gọi API backend được, và tối ưu tốc độ tải trang cho người dùng.
