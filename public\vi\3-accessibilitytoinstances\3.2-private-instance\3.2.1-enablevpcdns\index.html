<!DOCTYPE html>
<html lang="vi" class="js csstransforms3d">
  <head><script src="/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=livereload" data-no-instant defer></script>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="generator" content="Hugo 0.147.8">
    <meta name="description" content="">
<meta name="author" content="<EMAIL>">

    <link rel="icon" href="/images/favicon.png" type="image/png">

    <title>Kích hoạt DNS hostnames :: AWS System Manager</title>

    
    <link href="/css/nucleus.css?1751175618" rel="stylesheet">
    <link href="/css/fontawesome-all.min.css?1751175618" rel="stylesheet">
    <link href="/css/hybrid.css?1751175618" rel="stylesheet">
    <link href="/css/featherlight.min.css?1751175618" rel="stylesheet">
    <link href="/css/perfect-scrollbar.min.css?1751175618" rel="stylesheet">
    <link href="/css/auto-complete.css?1751175618" rel="stylesheet">
    <link href="/css/atom-one-dark-reasonable.css?1751175618" rel="stylesheet">
    <link href="/css/theme.css?1751175618" rel="stylesheet">
    <link href="/css/hugo-theme.css?1751175618" rel="stylesheet">
    
    <link href="/css/theme-workshop.css?1751175618" rel="stylesheet">
    
    

    <script src="/js/jquery-3.3.1.min.js?1751175618"></script>

    <style>
      :root #header + #content > #left > #rlblock_left{
          display:none !important;
      }
      
    </style>
    
  </head>
  <body class="" data-url="/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.1-enablevpcdns/">
    <nav id="sidebar" class="showVisitedLinks">

  
  
  <div id="header-wrapper">
    <div id="header">
      <a id="logo" href="/">

<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 30" width="30%"><defs><style>.cls-1{fill:#fff;}.cls-2{fill:#f90;fill-rule:evenodd;}</style></defs><title>AWS-Logo_White-Color</title><path class="cls-1" d="M14.09,10.85a4.7,4.7,0,0,0,.19,1.48,7.73,7.73,0,0,0,.54,**********,0,0,1,.***********,0,0,1-.32.49l-1,.7a.83.83,0,0,1-.***********,0,0,1-.49-.23,3.8,3.8,0,0,1-.6-.77q-.25-.42-.51-1a6.14,6.14,0,0,1-4.89,2.3,4.54,4.54,0,0,1-3.32-1.19,4.27,4.27,0,0,1-1.22-3.2A4.28,4.28,0,0,1,3.61,7.75,6.06,6.06,0,0,1,7.69,6.46a12.47,12.47,0,0,1,1.76.13q.92.13,1.91.36V5.73a3.65,3.65,0,0,0-.79-2.66A3.81,3.81,0,0,0,7.86,2.3a7.71,7.71,0,0,0-1.79.22,12.78,12.78,0,0,0-1.79.57,4.55,4.55,0,0,1-.58.22l-.26,0q-.35,0-.35-.52V2a1.09,1.09,0,0,1,.12-.58,1.2,1.2,0,0,1,.47-.35A10.88,10.88,0,0,1,5.77.32,10.19,10.19,0,0,1,8.36,0a6,6,0,0,1,4.35,1.35,5.49,5.49,0,0,1,1.38,4.09ZM7.34,13.38a5.36,5.36,0,0,0,1.72-.31A3.63,3.63,0,0,0,10.63,12,2.62,2.62,0,0,0,11.19,11a5.63,5.63,0,0,0,.16-1.44v-.7a14.35,14.35,0,0,0-1.53-.28,12.37,12.37,0,0,0-1.56-.1,3.84,3.84,0,0,0-2.47.67A2.34,2.34,0,0,0,5,11a2.35,2.35,0,0,0,.61,1.76A2.4,2.4,0,0,0,7.34,13.38Zm13.35,1.8a1,1,0,0,1-.64-.16,1.3,1.3,0,0,1-.35-.65L15.81,1.51a3,3,0,0,1-.15-.67.36.36,0,0,1,.41-.41H17.7a1,1,0,0,1,.65.16,1.4,1.4,0,0,1,.33.65l2.79,11,2.59-11A1.17,1.17,0,0,1,24.39.6a1.1,1.1,0,0,1,.67-.16H26.4a1.1,1.1,0,0,1,.67.16,1.17,1.17,0,0,1,.32.65L30,12.39,32.88,1.25A1.39,1.39,0,0,1,33.22.6a1,1,0,0,1,.65-.16h1.54a.36.36,0,0,1,.41.41,1.36,1.36,0,0,1,0,.26,3.64,3.64,0,0,1-.12.41l-4,12.86a1.3,1.3,0,0,1-.35.65,1,1,0,0,1-.64.16H29.25a1,1,0,0,1-.67-.17,1.26,1.26,0,0,1-.32-.67L25.67,3.64,23.11,14.34a1.26,1.26,0,0,1-.32.67,1,1,0,0,1-.67.17Zm21.36.44a11.28,11.28,0,0,1-2.56-.29,7.44,7.44,0,0,1-1.92-.67,1,1,0,0,1-.61-.93v-.84q0-.52.38-.52a.9.9,0,0,1,.31.06l.42.17a8.77,8.77,0,0,0,1.83.58,9.78,9.78,0,0,0,2,.2,4.48,4.48,0,0,0,2.43-.55,1.76,1.76,0,0,0,.86-1.57,1.61,1.61,0,0,0-.45-1.16A4.29,4.29,0,0,0,43,9.22l-2.41-.76A5.15,5.15,0,0,1,38,6.78a3.94,3.94,0,0,1-.83-2.41,3.7,3.7,0,0,1,.45-1.85,4.47,4.47,0,0,1,1.19-1.37A5.27,5.27,0,0,1,40.51.29,7.4,7.4,0,0,1,42.6,0a8.87,8.87,0,0,1,1.12.07q.57.07,1.08.19t.95.26a4.27,4.27,0,0,1,.7.29,1.59,1.59,0,0,1,.49.41.94.94,0,0,1,.15.55v.79q0,.52-.38.52a1.76,1.76,0,0,1-.64-.2,7.74,7.74,0,0,0-3.2-.64,4.37,4.37,0,0,0-2.21.47,1.6,1.6,0,0,0-.79,1.48,1.58,1.58,0,0,0,.49,1.18,4.94,4.94,0,0,0,1.83.92L44.55,7a5.08,5.08,0,0,1,2.57,1.6A3.76,3.76,0,0,1,47.9,11a4.21,4.21,0,0,1-.44,1.93,4.4,4.4,0,0,1-1.21,1.47,5.43,5.43,0,0,1-1.85.93A8.25,8.25,0,0,1,42.05,15.62Z"></path><path class="cls-2" d="M45.19,23.81C39.72,27.85,31.78,30,25,30A36.64,36.64,0,0,1,.22,20.57c-.51-.46-.06-1.09.56-.74A49.78,49.78,0,0,0,25.53,26.4,49.23,49.23,0,0,0,44.4,22.53C45.32,22.14,46.1,23.14,45.19,23.81Z"></path><path class="cls-2" d="M47.47,21.21c-.7-.9-4.63-.42-6.39-.21-.53.06-.62-.4-.14-.74,3.13-2.2,8.27-1.57,8.86-.83s-.16,5.89-3.09,8.35c-.45.38-.88.18-.68-.32C46.69,25.8,48.17,22.11,47.47,21.21Z"></path></svg>

</a>

    </div>
    
    <div class="searchbox">
    <label for="search-by"><i class="fas fa-search"></i></label>
    <input data-search-input id="search-by" type="search" placeholder="">
    <span data-search-clear=""><i class="fas fa-times"></i></span>
</div>

<script type="text/javascript" src="/%20js/lunr.min.js?1751175618"></script>
<script type="text/javascript" src="/%20js/auto-complete.js?1751175618"></script>
<script type="text/javascript">
    { { if hugo.IsMultilingual } }
    var baseurl = "\/\/localhost:1313\/\/vi";
    { { else } }
    var baseurl = "\/\/localhost:1313\/";
    { { end } }
</script>
<script type="text/javascript" src="/%20js/search.js?1751175618"></script>
    
  </div>

  <div class="highlightable">
    <ul class="topics">

      
      
      







<li data-nav-id="/vi/1-introduce/" title="Giới thiệu" class="dd-item 
        
        
        
        ">
  <a href="/vi/1-introduce/">
     <b> 1. </b> Giới thiệu
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



      
      







<li data-nav-id="/vi/2-prerequiste/" title="Chuẩn bị môi trường" class="dd-item 
        
        
        
        ">
  <a href="/vi/2-prerequiste/">
     <b> 2. </b> Chuẩn bị môi trường
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/vi/2-prerequiste/2.2-installnodejs/" title="Tạo IAM Role" class="dd-item 
        
        
        
        ">
  <a href="/vi/2-prerequiste/2.2-installnodejs/">
     <b> 2.2 </b> Tạo IAM Role
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/vi/2-prerequiste/2.1-installhugo/" title="Cài đặt Hugo" class="dd-item 
        
        
        
        ">
  <a href="/vi/2-prerequiste/2.1-installhugo/">
     <b> 2.1 </b> Cài đặt Hugo
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/vi/2-prerequiste/2.1-installhugo/2.1.2-createpublicsubnet/" title="Tạo Public subnet" class="dd-item 
        
        
        
        ">
  <a href="/vi/2-prerequiste/2.1-installhugo/2.1.2-createpublicsubnet/">
     <b> 2.1.2 </b> Tạo Public subnet
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/vi/2-prerequiste/2.1-installhugo/2.1.3-createprivatesubnet/" title="Tạo Private subnet" class="dd-item 
        
        
        
        ">
  <a href="/vi/2-prerequiste/2.1-installhugo/2.1.3-createprivatesubnet/">
     <b> 2.1.3 </b> Tạo Private subnet
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/vi/2-prerequiste/2.1-installhugo/2.1.4-createsecgroup/" title="Tạo các security group" class="dd-item 
        
        
        
        ">
  <a href="/vi/2-prerequiste/2.1-installhugo/2.1.4-createsecgroup/">
     <b> 2.1.4 </b> Tạo các security group
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/vi/2-prerequiste/2.1-installhugo/2.1.5-createec2linux/" title="Tạo Public Linux EC2" class="dd-item 
        
        
        
        ">
  <a href="/vi/2-prerequiste/2.1-installhugo/2.1.5-createec2linux/">
     <b> 2.1.5 </b> Tạo Public Linux EC2
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/vi/2-prerequiste/2.1-installhugo/2.1.6-createec2windows/" title="Tạo Private Windows EC2" class="dd-item 
        
        
        
        ">
  <a href="/vi/2-prerequiste/2.1-installhugo/2.1.6-createec2windows/">
     <b> 2.1.6 </b> Tạo Private Windows EC2
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
  </ul>
  
</li>



    
    
    
  </ul>
  
</li>



      
      







<li data-nav-id="/vi/3-accessibilitytoinstances/" title="Tạo kết nối đến máy chủ EC2" class="dd-item 
        parent
        
        
        ">
  <a href="/vi/3-accessibilitytoinstances/">
     <b> 3. </b> Tạo kết nối đến máy chủ EC2
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/vi/3-accessibilitytoinstances/3.1-public-instance/" title="Kết nối đến máy chủ Public" class="dd-item 
        
        
        
        ">
  <a href="/vi/3-accessibilitytoinstances/3.1-public-instance/">
     <b> 3.1. </b> Kết nối đến máy chủ Public
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/vi/3-accessibilitytoinstances/3.2-private-instance/" title="Tạo kết nối đến máy chủ EC2 Private" class="dd-item 
        parent
        
        
        ">
  <a href="/vi/3-accessibilitytoinstances/3.2-private-instance/">
     <b> 3.2. </b> Tạo kết nối đến máy chủ EC2 Private
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.1-enablevpcdns/" title="Kích hoạt DNS hostnames" class="dd-item 
        
        active
        
        ">
  <a href="/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.1-enablevpcdns/">
     <b> 3.2.1 </b> Kích hoạt DNS hostnames
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.2-createvpcendpoint/" title="Tạo VPC Endpoint" class="dd-item 
        
        
        
        ">
  <a href="/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.2-createvpcendpoint/">
     <b> 3.2.2 </b> Tạo VPC Endpoint
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.2-createvpcendpoint/*******-endpointssm/" title="Tạo Endpoint ssm" class="dd-item 
        
        
        
        ">
  <a href="/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.2-createvpcendpoint/*******-endpointssm/">
     <b> ******* </b> Tạo Endpoint ssm
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.2-createvpcendpoint/3.2.2.2-endpointssmmessages/" title="Tạo Endpoint ssmmessages" class="dd-item 
        
        
        
        ">
  <a href="/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.2-createvpcendpoint/3.2.2.2-endpointssmmessages/">
     <b> 3.2.2.2 </b> Tạo Endpoint ssmmessages
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.2-createvpcendpoint/3.2.2.3-endpointec2messages/" title="Tạo Endpoint ec2messages" class="dd-item 
        
        
        
        ">
  <a href="/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.2-createvpcendpoint/3.2.2.3-endpointec2messages/">
     <b> 3.2.2.3 </b> Tạo Endpoint ec2messages
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
  </ul>
  
</li>



    
    
    
    







<li data-nav-id="/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.3-connectec2/" title="Kết nối EC2 Private" class="dd-item 
        
        
        
        ">
  <a href="/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.3-connectec2/">
     <b> 3.2.3 </b> Kết nối EC2 Private
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
  </ul>
  
</li>



    
    
    
  </ul>
  
</li>



      
      







<li data-nav-id="/vi/4-s3log/" title="Quản lý session logs" class="dd-item 
        
        
        
        ">
  <a href="/vi/4-s3log/">
     <b> 4. </b> Quản lý session logs
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
  <ul>
    
    
    
    
    

    
    
    
    







<li data-nav-id="/vi/4-s3log/4.1-updateiamrole/" title="Cập nhật IAM Role" class="dd-item 
        
        
        
        ">
  <a href="/vi/4-s3log/4.1-updateiamrole/">
     <b> 4.1 </b> Cập nhật IAM Role
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/vi/4-s3log/4.2-creates3bucket/" title="Tạo S3 Bucket" class="dd-item 
        
        
        
        ">
  <a href="/vi/4-s3log/4.2-creates3bucket/">
     <b> 4.2 </b> Tạo S3 Bucket
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/vi/4-s3log/4.3-creategwes3/" title="Tạo S3 Gateway endpoint" class="dd-item 
        
        
        
        ">
  <a href="/vi/4-s3log/4.3-creategwes3/">
     <b> 4.3 </b> Tạo S3 Gateway endpoint
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
    







<li data-nav-id="/vi/4-s3log/4.4-configsessionlogs/" title="Theo dõi session logs" class="dd-item 
        
        
        
        ">
  <a href="/vi/4-s3log/4.4-configsessionlogs/">
     <b> 4.4 </b> Theo dõi session logs
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



    
    
    
  </ul>
  
</li>



      
      







<li data-nav-id="/vi/5-portfwd/" title="Port Forwarding" class="dd-item 
        
        
        
        ">
  <a href="/vi/5-portfwd/">
     <b> 5. </b> Port Forwarding
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



      
      







<li data-nav-id="/vi/6-cleanup/" title="Dọn dẹp tài nguyên  " class="dd-item 
        
        
        
        ">
  <a href="/vi/6-cleanup/">
    <b>6. </b>Dọn dẹp tài nguyên  
    
    <i class="fas fa-check read-icon"></i>
    
  </a>
  
  
</li>



      
      
    </ul>

    
    
    <section id="shortcuts">
      <h3>More</h3>
      <ul>
        
        <li>
          <a class="padding" href="https://www.facebook.com/groups/awsstudygroupfcj/"><i class='fab fa-facebook'></i> AWS Study Group</a>
        </li>
        
      </ul>
    </section>
    

    
    <section id="prefooter">
      <hr />
      <ul>
        
        <li>
          <a class="padding">
            <i class="fas fa-language fa-fw"></i>
            <div class="select-style">
              <select id="select-language" onchange="location = this.value;">
                
                
                
                
                
                
                
                
                <option id="en" value="//localhost:1313/3-accessibilitytoinstances/3.2-private-instance/3.2.1-enablevpcdns/">English
                </option>
                
                
                
                
                
                
                
                
                
                
                
                
                
                <option id="vi" value="//localhost:1313/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.1-enablevpcdns/" selected>Tiếng Việt</option>
                
                
                
                
              </select>
              <svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="255px" height="255px"
                viewBox="0 0 255 255" style="enable-background:new 0 0 255 255;" xml:space="preserve">
                <g>
                  <g id="arrow-drop-down">
                    <polygon points="0,63.75 127.5,191.25 255,63.75 		" />
                  </g>
                </g>
              </svg>
            </div>
          </a>
        </li>
        

        
        <li><a class="padding" href="#" data-clear-history-toggle=""><i class="fas fa-history fa-fw"></i> Clear History</a></li>
        
      </ul>
    </section>
    
    <section id="footer">
      <left>
    
     <b> Workshop</b> <br>
    <img src="https://hitwebcounter.com/counter/counter.php?page=7920860&style=0038&nbdigits=9&type=page&initCount=0" title="Migrate" Alt="web counter"   border="0" /></a>  <br>
     <b> <a href="https://cloudjourney.awsstudygroup.com/">Cloud Journey</a></b> <br>
    <img src="https://hitwebcounter.com/counter/counter.php?page=7830807&style=0038&nbdigits=9&type=page&initCount=0" title="Total CLoud Journey" Alt="web counter"   border="0"   />
     
</left>
<left>
    <br>
    <br>
        <b> Last Updated </b> <br>
        <i><font color=orange>30-01-2022</font></i>
    </left>
    <left>
        <br>
        <br>
            <b> Team </b> <br>
           
            <i> <a href="https://www.linkedin.com/in/sutrinh/"  style="color:orange">Sử Trịnh  </a> <br>
                <a href="https://www.linkedin.com/in/jotaguy"  style="color:orange">Gia Hưng </a> <br>
                <a href="https://www.linkedin.com/in/hiepnguyendt"  style="color:orange">Thanh Hiệp </a>
               
        </i>
        </left>

<script async defer src="https://buttons.github.io/buttons.js"></script>

    </section>
  </div>
</nav>



        <section id="body">
        <div id="overlay"></div>
        <div class="padding highlightable">
              
              <div>
                <div id="top-bar">
                
                
                <div id="breadcrumbs" itemscope="" itemtype="http://data-vocabulary.org/Breadcrumb">
                    <span id="sidebar-toggle-span">
                        <a href="#" id="sidebar-toggle" data-sidebar-toggle="">
                          <i class="fas fa-bars"></i>
                        </a>
                    </span>
                  
                  <span id="toc-menu"><i class="fas fa-list-alt"></i></span>
                  
                  <span class="links">
                 
                 
                    
          
          
            
            
          
          
            
            
          
          
            
            
          
          
            <a href='/vi/'>Website thương mại điện tử</a> > <a href='/vi/3-accessibilitytoinstances/'>Tạo kết nối đến máy chủ EC2</a> > <a href='/vi/3-accessibilitytoinstances/3.2-private-instance/'>Tạo kết nối đến máy chủ EC2 Private</a> > Kích hoạt DNS hostnames
          
        
          
        
          
        
          
        
                 
                  </span>
                </div>
                
                    <div class="progress">
    <div class="wrapper">
<nav id="TableOfContents">
  <ul>
    <li>
      <ul>
        <li></li>
      </ul>
    </li>
  </ul>
</nav>
    </div>
</div>

                
              </div>
            </div>
            
        <div id="head-tags">
        
        </div>
        
        <div id="body-inner">
          
            <h1>
              
              Kích hoạt DNS hostnames
            </h1>
          

        



	<h4 id="kích-hoạt-tính-năng-dns-hostnames-trên-vpc">Kích hoạt tính năng DNS hostnames trên VPC.</h4>
<ol>
<li>Để tạo được VPC Endpoint chúng ta sẽ cần bật tính năng <strong>DNS hostnames</strong> trên VPC.</li>
</ol>
<ul>
<li>
<p>Truy cập đến <a href="https://console.aws.amazon.com/vpc/home">giao diện quản trị của dịch vụ VPC</a></p>
</li>
<li>
<p>Click <strong>Your VPCs</strong>.</p>
</li>
<li>
<p>Chọn <strong>Lab VPC</strong>.</p>
</li>
<li>
<p>Click <strong>Actions</strong>.</p>
</li>
<li>
<p>Click <strong>Edit DNS hostnames</strong>.</p>
</li>
<li>
<p>Click <strong>Endpoint</strong>, sau đó click <strong>Create Endpoint</strong>.</p>
</li>
</ul>
<p><img src="/images/3.connect/009-connect.png" alt="Connect"></p>
<ol start="2">
<li>Tại trang <strong>Edit DNS hostnames</strong>.</li>
</ol>
<ul>
<li>Click chọn <strong>Enable</strong>.</li>
<li>Click <strong>Save changes</strong>.</li>
</ul>
<p><img src="/images/3.connect/010-connect.png" alt="Connect"></p>





<footer class=" footline" >
	
</footer>

        
        </div> 
        

      </div>

    <div id="navigation">
        
        
        
        
            
            
                
                    
                    
                
                

                    
                    
                        
                    
                    

                    
                        
            
            
                
                    
                        
                        
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                        
                    
                    

                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                        
                    
                    

                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
            
        
                    
            
        
                    
                        
            
            
                
                    
                    
                
                

                    
                    
                        
                    
                    

                    
                        
            
            
                
                    
                        
                        
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                    
                
                

                    
                    
                        
                    
                    

                    
                        
            
            
                
                    
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                        
                        
                    
                
                

                    
                    
                        
                    
                    

                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
            
        
                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                        
                    
                    

                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
                        
            
            
                
                    
                
                

                    
                    
                    

                    
            
        
                    
            
        
        
        


	 
	 
		
			<a class="nav nav-prev" href="/vi/3-accessibilitytoinstances/3.2-private-instance/" title="Tạo kết nối đến máy chủ EC2 Private"> <i class="fa fa-chevron-left"></i></a>
		
		
			<a class="nav nav-next" href="/vi/3-accessibilitytoinstances/3.2-private-instance/3.2.2-createvpcendpoint/" title="Tạo VPC Endpoint" style="margin-right: 0px;"><i class="fa fa-chevron-right"></i></a>
		
	
    </div>

    </section>
    
    <div style="left: -1000px; overflow: scroll; position: absolute; top: -1000px; border: none; box-sizing: content-box; height: 200px; margin: 0px; padding: 0px; width: 200px;">
      <div style="border: none; box-sizing: content-box; height: 200px; margin: 0px; padding: 0px; width: 200px;"></div>
    </div>
    <script src="/js/clipboard.min.js?1751175618"></script>
    <script src="/js/perfect-scrollbar.min.js?1751175618"></script>
    <script src="/js/perfect-scrollbar.jquery.min.js?1751175618"></script>
    <script src="/js/jquery.sticky.js?1751175618"></script>
    <script src="/js/featherlight.min.js?1751175618"></script>
    <script src="/js/highlight.pack.js?1751175618"></script>
    <script>hljs.initHighlightingOnLoad();</script>
    <script src="/js/modernizr.custom-3.6.0.js?1751175618"></script>
    <script src="/js/learn.js?1751175618"></script>
    <script src="/js/hugo-learn.js?1751175618"></script>

    <link href="/mermaid/mermaid.css?1751175618" rel="stylesheet" />
    <script src="/mermaid/mermaid.js?1751175618"></script>
    <script>
        mermaid.initialize({ startOnLoad: true });
    </script>
    <script>
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

  ga('create', 'UA-158079754-2', 'auto');
  ga('send', 'pageview');

</script>
  </body>
</html>
