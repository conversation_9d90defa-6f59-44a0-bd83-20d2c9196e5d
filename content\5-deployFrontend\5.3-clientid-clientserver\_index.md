---
title : "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> Google OAuth2 Client ID và Client Secret"
date : 2023-10-25
weight : 8
chapter : false
pre : " <b> 5.3 </b> "
---

#### <PERSON><PERSON><PERSON> <PERSON>ình Google OAuth2 Client ID và Client Secret
Sau khi đã tạo Google OAuth2 Project và bật các API cần thiết ở bước chuẩn bị môi trường, bạn cần tạo **OAuth2 Client ID và Client Secret** để tích hợp đăng nhập Google cho hệ thống thương mại điện tử.

**<PERSON><PERSON><PERSON> bước thực hiện:**

1. **T<PERSON>y cập vào Google Cloud Console**
   - Mở [Google Cloud Console](https://console.cloud.google.com/) và chọn đúng project đã tạo ở bước trước.
 ![api_services](/images/api_services.png)


2. **Tạo OAuth2 Client ID**
   - Vào **APIs & Services** → **Credentials**.
![credentials](/images/credentials.png)

   - Click vào **+ Create Credentials** → **OAuth client ID**.
![create_oAuth_client_id](/images/oAuth_client_id.png)

   - Click vào **Configure consent srcreen**.
![configure_consent_screen](/images/configure_consent_screen.png)

   - Click vào **Get Started**.
![get_started](/images/get_started.png)

   - Bạn điền các thông tin như sau:
     - App name: `FcjFashionShop`
     - User support email: **Nhập email của bạn**
     - Nhấn vào **Next**
![information_google_oauth](/images/information_google_oauth.png)

     - Email addresses: **Nhập email của bạn**.
     - Nhấn vào **Next**
![contact_information](/images/contact_information.png)

     - Tích chọn **I agree to the Google API Services: User Data Policy.**
     - Chọn **Continue** và chọn **Create**
![finish_create_oauth](/images/finish_create_oauth.png)

   - Tại **Metrics** click vào **Create OAuth client**
![select_create_oauth_client](/images/select_create_oauth_client.png)

   - Tại Application type chọn **Web application**
   -  Name: `FcjFashionShop`
![info_oauth_client_id](/images/info_oauth_client_id.png)

   - Tại Authorized JavaScript origins
     - Chọn **Add URI** để thêm URl mới
     - Dán URL của **S3 Bucket website endpoint** bạn đã copy trước đó
![create_oauth_client_id](/images/create_oauth_client_id.png)


   - Nếu chưa cấu hình **OAuth consent screen**:
     - Làm theo hướng dẫn, nhập tên app, email, logo (nếu có), lưu lại.
   - **Chọn loại ứng dụng**:
     - Chọn **Web application** (nếu website) hoặc **Other/SPA** nếu là ứng dụng đơn trang.
   - **Điền Authorized redirect URIs** (cần chính xác):
     - Nếu dùng backend:  
       ```
       https://your-backend-domain.com/api/auth/google/callback
       ```
     - Nếu test local:  
       ```
       http://localhost:3000/api/auth/google/callback
       ```
     - Thêm domain S3 nếu frontend cũng xác thực trực tiếp với Google.

1. **Lấy Client ID và Client Secret**

   - Sau khi tạo, Google sẽ hiển thị **Client ID** và **Client Secret**.
   - **Sao chép lại, lưu trữ cẩn thận.**
   - Thông tin này sẽ cần nhập vào:
     - Biến môi trường backend (`GOOGLE_CLIENT_ID`, `GOOGLE_CLIENT_SECRET`)
     - File cấu hình frontend nếu xác thực phía client.

2. **Cấu hình vào project**

   - Với backend (Node.js, Java, ...):
     - Thêm vào file `.env`:
       ```
       GOOGLE_CLIENT_ID=xxx.apps.googleusercontent.com
       GOOGLE_CLIENT_SECRET=xxxxxxxxxxxxxx
       ```
   - Với frontend (React, Vue...):
     - Thường chỉ cần **Client ID** (tuyệt đối không để lộ Client Secret trên client!)
     - Thêm vào biến môi trường (nếu cần):
       ```
       REACT_APP_GOOGLE_CLIENT_ID=xxx.apps.googleusercontent.com
       ```

{{% notice warning %}}
**Lưu ý bảo mật:**  
Không public **Client Secret** lên Github hoặc bất cứ đâu!  
Chỉ sử dụng Client ID cho phía frontend, Client Secret dùng cho phía backend/server.
{{% /notice %}}

{{% notice tip %}}
**Mẹo:**  
Nên tạo riêng các Client ID cho môi trường dev, staging, production để dễ quản lý và bảo mật.
{{% /notice %}}

**Kết luận:**  
Sau khi hoàn thành các bước này, bạn đã có đủ thông tin để cấu hình Google OAuth2 cho cả backend và frontend, sẵn sàng triển khai tính năng đăng nhập Google an toàn, chuyên nghiệp cho dự án thương mại điện tử trên AWS.

