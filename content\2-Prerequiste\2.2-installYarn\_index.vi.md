---
title : "Cài đặt Yarn"
date : 2023-10-25
weight : 4
chapter : false
pre : " <b> 2.2 </b> "
---

Yarn là một trình quản lý gói (package manager) cho <PERSON>, được sử dụng rộng rãi trong các dự án frontend để giúp quản lý các thư viện và gói phần mềm một cách hiệu quả. Yarn giúp giải quyết các vấn đề về tốc độ cài đặt, sự nhất quán và bảo mật khi quản lý các thư viện cho dự án.

#### Bước 1: Cài đặt Yarn

1. **Cài đặt Yarn qua npm:**
   - Yarn có thể được cài đặt thông qua npm (Node Package Manager). Đảm bảo rằng bạn đã cài đặt **NodeJS** và **npm** trước khi tiếp tục.
   - <PERSON><PERSON> cài đặt Yarn, mở terminal hoặc Command Prompt và chạy lệnh sau:

         npm install -g yarn


2. **Cài đặt Yarn trên macOS (qua Homebrew):**
   - Nếu bạn đang sử dụng macOS, bạn có thể cài đặt Yarn qua **Homebrew** bằng lệnh sau:

         brew install yarn


3. **Cài đặt Yarn trên Linux (Ubuntu):**
   - Để cài đặt Yarn trên Ubuntu, bạn cần thêm kho lưu trữ của Yarn vào hệ thống và chạy lệnh sau:

         sudo apt update && sudo apt install yarn


   {{% notice success %}}
   Yarn đã được cài đặt thành công! Bạn có thể bắt đầu quản lý các thư viện frontend cho dự án của mình.
   {{% /notice %}}
   

#### Bước 2: Kiểm tra cài đặt Yarn

1. **Kiểm tra phiên bản Yarn:**
   - Sau khi cài đặt thành công, bạn có thể kiểm tra phiên bản Yarn bằng lệnh:

         yarn --version

   - Nếu hiển thị ra phiên bản yarn, bạn đã cài đặt yarn thành công:

          C:\Users\<USER>