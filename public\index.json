[{"uri": "//localhost:1313/1-introduce/", "title": "Introduction", "tags": [], "description": "", "content": "System Introduction This workshop will guide you through building and deploying a dynamic e-commerce website on AWS&rsquo;s modern serverless platform.\nYou will leverage core components such as API Gateway, AWS Certificate Manager, IAM, S3, Lambda, CloudFormation, CloudFront, DynamoDB, Route 53, CloudWatch, and SAM CLI to automate, scale, and optimize the cost of your system.\nOverall Architecture Below is the overall architecture of the system.\nFrontend: Uses a Hugo theme, stored statically on Amazon S3 and distributed via CloudFront. Backend: Communicates via RESTful API with API Gateway and processes business logic with AWS Lambda. Database: Uses DynamoDB for dynamic, easily scalable data storage. Infrastructure Management: CloudFormation automatically creates and configures AWS resources; SAM CLI is used for deploying and automating serverless infrastructure. Domain &amp; Operations Management: Route 53 manages domain/DNS; CloudWatch monitors, logs, and provides alerts for the entire system; AWS Certificate Manager provides free SSL certificates for the website. Data Flow Diagram The following diagram illustrates the main data flow and interactions between system components.\n(Insert data flow or sequence diagram here)\nList of AWS Services Used API Gateway: Creates REST APIs for frontend-backend communication. S3: Stores static website assets and resources. Lambda: Handles serverless backend logic. DynamoDB: Dynamic, scalable NoSQL database. CloudFormation: Infrastructure as Code (IaC) management. Route 53: Domain registration and DNS configuration. CloudFront: Global content delivery (CDN), accelerates access and enhances security. AWS Certificate Manager: Free SSL certificate provisioning and management. IAM: AWS account management and resource access control. CloudWatch: System monitoring, logging, and alerting. SAM CLI: Automates deployment of serverless resources. Key Features of the Website Fully serverless architecture on AWS. Deploy a secure, auto-scaling dynamic website. User-friendly interface with Hugo. Dynamic backend using RESTful APIs via API Gateway &amp; Lambda. Dynamic data storage with DynamoDB. Real-time data processing with Lambda &amp; DynamoDB. Integrated monitoring, logging, DNS, CDN, and SSL auto-configuration. Infrastructure as Code (IaC) with CloudFormation &amp; SAM CLI. "}, {"uri": "//localhost:1313/2-prerequiste/", "title": "Preparation", "tags": [], "description": "", "content": "Before you begin deploying your dynamic e-commerce website using AWS services like API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, and SAM CLI, you need to complete a few basic environment preparation steps. These tools and services will help you quickly, securely, and efficiently deploy your application.\nIn this section, you will perform the necessary setup steps for both the frontend and backend environments of the project:\nInstall NodeJS and Yarn: To support frontend build and management. Install SAM CLI: To deploy the serverless backend on AWS. Create AWS account and configure IAM: To set up access and security for the AWS environment. These preparation steps will ensure that you have the proper development environment in place and working correctly before proceeding with deploying AWS services for your project.\n⚠️ Note: Make sure you have all the tools installed before starting to work with AWS services. Otherwise, you may encounter issues during deployment.\nContent Install NodeJS Install Yarn for frontend Install SAM CLI for backend Create AWS Account &amp; Configure IAM Create Google OAuth2 Project "}, {"uri": "//localhost:1313/3-deploybackend/", "title": "Deploying the Backend", "tags": [], "description": "", "content": "Deploying the backend for your dynamic e-commerce website is accomplished using the AWS SAM CLI in combination with CloudFormation. With these tools, you can easily deploy serverless services such as API Gateway, Lambda, and DynamoDB.\nMain Tools Used: SAM CLI: A command-line tool for developing, testing, and deploying serverless applications. SAM CLI makes it easy to deploy API Gateway, Lambda functions, and other serverless resources on AWS. CloudFormation: An infrastructure as code service that allows you to automate the deployment and management of AWS resources. In this section, you will learn how to deploy a serverless backend for your website using SAM CLI, including the creation of resources such as API Gateway, Lambda, and DynamoDB.\nNote: Please ensure you have completed the environment preparation steps (including installing the SAM CLI and configuring AWS IAM) before starting the backend deployment.\nContents Deploying the backend with SAM CLI/CloudFormation Managing backend resources with CloudFormation/SAM CLI "}, {"uri": "//localhost:1313/2-prerequiste/2.1-installnodejs/", "title": "Installing NodeJS", "tags": [], "description": "", "content": "NodeJS is a powerful and popular JavaScript runtime environment, widely used for developing dynamic web applications. In this step, you will install NodeJS on your operating system to build your dynamic e-commerce website.\nStep 1: Install NodeJS Download NodeJS from the official website:\nVisit the official NodeJS website at Node.js and download the LTS version for your operating system (Windows, macOS, or Linux). Download the .msi file for Windows, .pkg for macOS, or .tar.xz for Linux. Install NodeJS on Windows:\nAfter downloading the .msi file, open it and follow the installation instructions. Make sure to check Add to PATH during the installation process. Install NodeJS on macOS:\nInstall via Homebrew: brew install node Install NodeJS on Linux (Ubuntu):\nUse the following command to install NodeJS:\nsudo apt install nodejs\rVerify the installation:\nAfter installation, open your terminal (or Command Prompt on Windows) and run the following command to check the NodeJS version:\nnode -v\rIf the installation is successful, you will see the NodeJS version displayed.\nExample\nAfter installation, open your terminal (or Command Prompt on Windows) and enter the following command to check the NodeJS version:\nC:\\Users\\<USER>"}, {"uri": "//localhost:1313/3-deploybackend/3.1-deploy-backend/", "title": "Deploying the Backend with SAM CLI/CloudFormation", "tags": [], "description": "", "content": "Deploying the Backend with SAM CLI/CloudFormation In this section, you will deploy the backend for your dynamic e-commerce website using SAM CLI and CloudFormation. We will use the provided template.yaml file in your project to automatically create resources such as API Gateway, Lambda, and DynamoDB.\nStep 1: Configure AWS CLI Before using SAM CLI for deployment, you need to configure AWS CLI with your AWS account credentials. This allows SAM CLI to use the access rights granted to your IAM User.\nRun the aws configure command:\nOpen your terminal and enter the following command to configure AWS CLI:\naws configure\rEnter your configuration information:\nAWS Access Key ID: Enter the Access Key ID generated when creating your IAM User.\nAWS Secret Access Key: Enter the Secret Access Key associated with your Access Key ID.\nDefault region name: Enter ap-southeast-1 (Singapore).\nDefault output format: You can enter json or leave it as the default (None). Once finished, AWS CLI will save the configuration in the ~/.aws/credentials file (Linux/macOS) or C:\\Users\\<USER>\\.aws\\credentials (Windows).\nStep 2: Deploy the Backend with SAM CLI Check the template.yaml file:\nIf you don’t have a template.yaml file, check your project directory. This file defines resources like API Gateway, Lambda, and DynamoDB. If the file exists, make sure it is correctly configured for the resources you need. Build the project:\nAfter ensuring that template.yaml is in your project,\nNavigate to your backend project folder and use SAM CLI to build:\nsam build\rIf you see Build Succeeded, you have successfully installed and configured SAM CLI. Validate the Template:\nCheck your template.yaml configuration to ensure there are no syntax or configuration errors:\nRun sam validate to check your template:\nsam validate\rIf you see the message below, your template.yaml is valid. Deploy resources to AWS:\nAfter building and validating, deploy resources to AWS using:\nsam deploy --guided\rWhen using SAM CLI to deploy, you’ll be prompted for configuration options. Suggested values:\nStack Name: sam-app AWS Region: ap-southeast-1 Confirm changes before deploy: y Allow SAM CLI IAM role creation: y Disable rollback: n (do not disable rollback) ExpressApiFunction has no authentication. Is this okay?: y Save arguments to configuration file: y SAM configuration file: Press Enter SAM configuration environment: Press Enter Deploy this changeset: y Wait about 5 minutes for serverless resources (API Gateway, Lambda, DynamoDB, etc.) to be set up from your machine to AWS. Verify Deployment:\nAfter successful deployment, SAM CLI will provide information about the resources created. You can check API Gateway, Lambda function, S3, CloudFormation, and DynamoDB in the AWS Management Console to confirm that everything has been set up correctly. Your backend has been successfully deployed! Resources like API Gateway, Lambda function, and DynamoDB have been automatically created. You can check them in the AWS Console.\nGo to API Gateway to see your API Gateway. Go to Lambda function to see your Lambda functions. Go to Amazon S3 to see your S3 buckets. Go to CloudFormation to see your CloudFormation stacks. Go to DynamoDB to see your DynamoDB tables. "}, {"uri": "//localhost:1313/2-prerequiste/2.2-installyarn/", "title": "Installing Yarn", "tags": [], "description": "", "content": "Yarn is a package manager for JavaScript that is widely used in frontend projects to efficiently manage libraries and software packages. Yarn helps address issues related to installation speed, consistency, and security when managing project dependencies.\nStep 1: Install Yarn Install Yarn via npm:+\nYarn can be installed through npm (Node Package Manager). Make sure you have NodeJS and npm installed before proceeding.\nTo install Yarn, open your terminal or Command Prompt and run the following command:\nnpm install -g yarn\rInstall Yarn on macOS (via Homebrew):\nIf you are using macOS, you can install Yarn via Homebrew with the following command:\nbrew install yarn\rInstall Yarn on Linux (Ubuntu):\nTo install Yarn on Ubuntu, you need to add the Yarn repository to your system and run the following command:\nsudo apt update &amp;&amp; sudo apt install yarn\rYarn has been installed successfully! You can now start managing frontend libraries for your project.\nStep 2: Verify Yarn Installation Check Yarn version:\nAfter successfully installing Yarn, you can verify the version by running:\nyarn --version\rIf the version number is displayed, Yarn has been installed successfully:\nC:\\Users\\<USER>"}, {"uri": "//localhost:1313/4-testbackendapi/", "title": "Testing Backend APIs with Postman", "tags": [], "description": "", "content": "Testing Backend APIs with Postman After successfully deploying your backend with API Gateway and Lambda, the next step is to test your APIs to verify that the backend system is functioning correctly before integrating with the frontend.\nPostman is a powerful and widely-used tool that allows you to send requests (GET, POST, PUT, DELETE, &hellip;) to API endpoints, view responses, verify processing logic, and debug errors.\nYou can download Postman here: Download Postman\nObjectives of this section:\nGuide you on how to retrieve the API Gateway endpoint that you just deployed on AWS to use with Postman. Practice sending GET/POST requests to the API, analyze the returned responses to verify backend correctness. Identify and troubleshoot common errors encountered during API testing. This section covers:\nRetrieving the API Gateway endpoint:\nInstructions on how to locate and copy the endpoint URL from the AWS Console for API testing purposes. Sending GET/POST requests to verify backend responses:\nStep-by-step guidance on how to send GET/POST requests with Postman, enter payload data, and review backend responses. Note:\nBefore testing APIs with Postman, make sure that the backend has been deployed successfully and there are no errors from previous steps (CloudFormation/Lambda must be in a successful state).\nBy the end of this section, you will:\nKnow how to obtain the API Gateway endpoint for testing. Be proficient in using Postman to send GET/POST requests to your backend. Be able to analyze and verify API responses, and be ready to connect your tested backend with the frontend. Contents Retrieve the API Gateway endpoint Send GET/POST requests to verify backend responses "}, {"uri": "//localhost:1313/3-deploybackend/3.2-check-status-log-backend/", "title": "Checking Backend Status and Logs After Deployment", "tags": [], "description": "", "content": "Checking Backend Status and Logs After Deployment After deploying your backend on AWS using SAM CLI and CloudFormation, the next crucial step is to check the resource status and review backend logs to ensure all components are functioning correctly.\nMake sure that API Gateway, Lambda, DynamoDB, etc. have been created successfully and are running. Quickly identify and resolve deployment or logic errors if any issues occur. Prepare for API testing using Postman or your frontend application. Check CloudFormation Stack and Resource Status\nAccess the AWS CloudFormation Console. Select the recently deployed Stack (e.g., sam-app). In the Status tab, if the status is CREATE_COMPLETE or UPDATE_COMPLETE, your deployment was successful. In the Resources tab, view the list of created resources (API Gateway, Lambda, DynamoDB, S3, etc.). If you see a status like ROLLBACK or FAILED, click on the Events tab to view error details and identify the root cause.\nView Lambda Logs with CloudWatch\nIn the AWS Console, go to Lambda → select your deployed function. Scroll down and click on the Monitor tab, then select View CloudWatch logs to access detailed logs. Scroll down and click on the Log streams tab, then select the first log stream. Each time a request is made (GET/POST, etc.), <PERSON><PERSON> writes logs to CloudWatch. Here you can:\nView received requests. Check returned responses. Debug issues if the code crashes or returns a 500 error. Verify API Gateway Functionality\nIn the AWS Console, go to API Gateway → select the API you just created. Go to Stages → select the relevant stage → choose prod.\nCopy the Invoke URL to prepare for testing with Postman/curl in the next step. Once you&rsquo;ve verified that all resources are functioning properly and there are no critical errors in the logs, you&rsquo;re ready to test your backend API using Postman or integrate with your frontend!\n"}, {"uri": "//localhost:1313/5-deployfrontend/", "title": "Deploy Frontend", "tags": [], "description": "", "content": "Deploy Frontend After the backend is ready and has passed all tests, the next step is to deploy the website frontend to AWS S3 as a static website, optimizing for both cost and page load speed.\nAt the same time, you will configure hosting, caching, and CORS so the frontend works smoothly and securely when connecting to the backend API.\nObjectives of this section:\nGuide you through uploading and deploying your frontend code to an AWS S3 Bucket. Enable static website hosting, configure cache and CORS so your frontend is accessible, stable, and secure. Grant public permissions to your S3 Bucket so the website can be accessed publicly. Connect the frontend to the backend API endpoint that has been successfully tested in previous steps. Note:\nMake sure you have installed yarn in your project directory.\nBy the end of this section, you will:\nSuccessfully deploy your frontend website to S3, ensuring public accessibility. Master hosting, caching, and CORS configuration—enabling your website to load quickly, safely, and remain compatible with the backend. Know how to grant public permissions to your S3 bucket, avoiding 403 errors when accessing your website. Be ready to upgrade your system with custom domains, CloudFront, or other new features in the next steps. Contents Deploy frontend to S3 Bucket Enable static hosting, configure CORS Grant public permissions to S3 Bucket Configure Google OAuth2 Client ID and Client Secret Connect frontend with backend API "}, {"uri": "//localhost:1313/2-prerequiste/2.3-installsamcli/", "title": "Installing SAM CLI", "tags": [], "description": "", "content": "The AWS SAM CLI is a command-line tool that helps you develop, package, and deploy serverless applications using AWS Lambda, API Gateway, and other AWS serverless services. In this step, you will install the SAM CLI to develop the serverless backend for your dynamic e-commerce website.\nStep 1: Install the SAM CLI Install on Windows:\nGo to the Installing the AWS SAM CLI page to download the SAM CLI installer for Windows. Select Windows. Run the .msi installer and follow the instructions to complete the installation. Open your terminal and enter sam --version. Install on macOS:\nUse Homebrew to install the SAM CLI:\nbrew tap aws/tap\rbrew install aws-sam-cli\rInstall on Linux (Ubuntu):\nOn Linux, you can use apt to install the SAM CLI:\nsudo apt-get update\rsudo apt-get install aws-sam-cli\rInstall SAM CLI from source (if needed):\nIf you prefer not to use the methods above, you can install the SAM CLI from source. Detailed instructions can be found at: Install SAM CLI from source. The SAM CLI has been installed successfully! You can now start developing and deploying serverless applications with AWS.\nStep 2: Verify SAM CLI Installation Check the SAM CLI version:\nAfter a successful installation, open your terminal (or Command Prompt on Windows) and enter the following command to check the SAM CLI version:\nsam --version\rVerify SAM CLI installation:\nIf the above command displays the version information of the SAM CLI, the tool has been installed successfully.\nC:\\Users\\<USER>"}, {"uri": "//localhost:1313/4-testbackendapi/4.1-endpoint-api-gateway/", "title": "Retrieving the API Gateway Endpoint", "tags": [], "description": "", "content": "Retrieving the API Gateway Endpoint After deploying the backend using SAM CLI/CloudFormation, the system will automatically generate an endpoint address for your API Gateway. This is the address you will use to send test requests via Postman, curl, or integrate with your frontend later.\nNote:\nUse the previously saved endpoint if you already have it.\nSteps to follow:\nAccess the AWS Management Console\nLog in to the AWS Console. Select API Gateway from the list of AWS services. Select the Recently Deployed API\nIn the API Gateway interface, you&rsquo;ll see a list of existing APIs. Find and click on the name of the API you just deployed. Retrieve the Endpoint from Stages\nIn the left-hand menu, select Stages. Click on the stage you have deployed (e.g., prod). At the top, you&rsquo;ll see the Invoke URL. This is the API Gateway endpoint you will use for testing. Copy this URL. Verify the Endpoint\nYou can copy this endpoint and paste it into your browser or Postman to check the basic response. Typically, if you haven&rsquo;t configured specific resources (e.g., /products), accessing the root endpoint will return a “Missing Authentication Token” error or HTTP 403. This is normal—just make sure to append the correct resource route when testing your API. Tip:\nIt is recommended to save this endpoint in your project README or personal notes to avoid having to revisit the AWS Console multiple times during API testing.\n"}, {"uri": "//localhost:1313/2-prerequiste/2.4-createiam/", "title": "Create Account and Configure IAM", "tags": [], "description": "", "content": "Create Account and Configure IAM AWS Identity and Access Management (IAM) allows you to securely manage access to AWS resources. In this step, you will create an IAM user, grant appropriate permissions, and configure an Access Key for use in subsequent steps of the workshop.\nCreate IAM User Go to the AWS Management Console and navigate to the IAM service.\nIn the left navigation pane, select Users and click Create users from the left menu.\nCreate the IAM user:\nSet the User name as Admin<PERSON>ser (or any name you prefer). Check Provide user access to the AWS Management Console – optional. Under User type, select I want to create an IAM user. Choose Custom password – enter your password. Uncheck Users must create a new password at next sign-in – Recommended. Click Next. Grant permissions to the user:\nSelect Attach policies directly. Search for and select the following policies: AWSCloudFormationFullAccess, AmazonDynamoDBFullAccess, AWSLambda_FullAccess, AmazonS3FullAccess, AmazonAPIGatewayAdministrator, IAMFullAcces to grant the necessary project permissions. Scroll down and click Next. Review the information on the Permissions summary page.\nClick Create user to create the AWS IAM account. The IAM user has been created successfully.\nClick Download .csv file to save the account credentials. Click View user to see detailed information. Create Access Key Display and save the Access Key and Secret Access Key:\nClick Create access key to generate an access key for the user. Check Command Line Interface (CLI) and click Next. Click Show to reveal the Secret access key value. Click Create access key. Click Download .csv file to store the credentials. Then click Done. "}, {"uri": "//localhost:1313/5-deployfrontend/5.1-frontend-s3/", "title": "Deploy Frontend to S3 Bucket", "tags": [], "description": "", "content": "Deploy Frontend to S3 Bucket To make your frontend website available online, you will upload all built static files to an AWS S3 Bucket. S3 acts as a static hosting solution, helping distribute your website at low cost and high speed.\nSteps to follow:\nStep 1: Build the Frontend Source Code Open the terminal in VSCode and navigate to the frontend directory. Copy and paste the following command into the terminal: yarn build The build folder will contain all static files that need to be uploaded. Wait about 1 minute for the build process to complete. Step 2: Create S3 Buckets Create an S3 Bucket for the Frontend Go to the AWS S3 Console. Click Create bucket. Enter a bucket name, for example: fcjfashionshop.com. Region: Select the same region as your backend (recommended: ap-southeast-1 for consistency). Disable “Block all public access”. Check I acknowledge that the current settings might result in this bucket and the objects within becoming public to acknowledge the warning and allow your website to be public. Click Create bucket to finish. Caution:\nA static website bucket must have public read access enabled for everyone. However, do not upload sensitive data to this bucket as anyone with the link can access it!\nCreate an S3 Bucket for Uploaded Images/Avatars\nGo to the AWS S3 Console. Click Create bucket. Enter a bucket name, for example: uploads-avatars-2025 (choose an identifiable, non-accented name). Region: Choose the same region as your system. Disable “Block all public access” (uncheck the box), acknowledge the warning to allow public access for image files. Check I acknowledge that the current settings might result in this bucket and the objects within becoming public. Click Create bucket to finish. Caution:\nIf you set the upload/avatar bucket to public, anyone with the file link can view the images. Do not upload sensitive information to this bucket!\nStep 3: Upload the Built Frontend to S3 Bucket Using AWS CLI Make sure you have installed and configured AWS CLI (ran aws configure).\nRun the following command:\ncd frontend\raws s3 cp build/ s3://fcjfashionshop.com/ --recursive The --recursive flag helps upload all files and subfolders inside the build directory.\nVerify Bucket Contents\nGo back to the bucket in the AWS Console and verify that all files (index.html, main.js, CSS, images, etc.) have been uploaded successfully.\nYou can click directly on a file (e.g., index.html) and copy its Object URL to check if the file is publicly accessible (you should see the HTML/raw file in your browser).\nYou can check the files and folders uploaded to S3 by browsing the Objects section.\nAfter completing the above steps, you have successfully uploaded your frontend website to AWS S3.\n"}, {"uri": "//localhost:1313/4-testbackendapi/4.2-get-post-response/", "title": "Sending GET/POST Requests to Verify Backend Responses", "tags": [], "description": "", "content": "Sending GET/POST Requests to Verify Backend Login Responses After obtaining the API Gateway endpoint, you need to send POST and GET requests to the API to confirm that the backend is functioning properly. This is typically done using Postman, the most popular API testing tool.\nSteps to follow:\nTesting POST Method with Postman\nOpen Postman, select New → HTTP Request.\nSet the method to POST.\nEnter the full endpoint URL, for example:\nhttps://ars3538v1i.execute-api.ap-southeast-1.amazonaws.com/Prod/api/users Switch to the Body tab → select raw → choose JSON. Enter sample data in the body as follows:\n{ &#34;username&#34;: &#34;AdminUser&#34;, &#34;email&#34;: &#34;<EMAIL>&#34;, &#34;password&#34;: &#34;AdminUser123@&#34;, &#34;firstName&#34;: &#34;Admin&#34;, &#34;lastName&#34;: &#34;User&#34;, &#34;isAdmin&#34;: true, &#34;googleId&#34;: null, &#34;avatar&#34;: &#34;https://i.imgur.com/avatar.jpg&#34;, &#34;phone&#34;: &#34;0912345678&#34;, &#34;address&#34;: &#34;&#34;, &#34;isDeleted&#34;: false } Click Send to submit the request.\nIf the API works correctly, you will receive a confirmation response.\nCopy the returned token to use for subsequent requests. If the API requires authentication (API Key, Bearer Token, etc.) or custom headers, add them in the Headers tab of Postman before sending the request!\nTesting GET Method for Login with Postman\nGo to DynamoDB on the AWS Console.\nSelect the ShopUser table. Choose Explore table items to check the data for the newly created user. Scroll down and edit the isAdmin field for the created user to true. Create a new request in Postman, set the method to GET.\nEnter the full endpoint URL, for example:\nhttps://ars3538v1i.execute-api.ap-southeast-1.amazonaws.com/Prod/api/users In the Authorization section, choose: Bearer Token.\nIn the Token field: paste the previously saved token.\nClick Send to submit the request. If the API is functioning correctly, you will receive a response with the user data from the database.\nCheck the HTTP status code (should be 200 OK) and the response body. You can test other project functionalities by reviewing the code to find the respective API URLs.\nAnalyzing Results and Handling Common Errors\nIf you receive the correct response data → the backend is working as expected. If you encounter errors like 403 Forbidden, 401 Unauthorized, or Missing Authentication Token: Verify the endpoint and ensure the correct resource route is used. Check the API Gateway configuration and mapping templates. Review the Lambda logs in CloudWatch for detailed error information. If you receive a 500 Internal Server Error: This is often due to an error in the Lambda code. Go to CloudWatch logs to debug. Conclusion:\nTesting the API using POST/GET requests is an essential step to confirm that the backend is functioning correctly before integrating with the frontend or deploying to production. If errors occur, check CloudWatch logs or review the API Gateway and Lambda configurations for timely troubleshooting.\n"}, {"uri": "//localhost:1313/6-ssl-s3-static/", "title": "Setup SSL S3 Static Website", "tags": [], "description": "", "content": "Set Up a Static Website with SSL on S3 If you want to deploy a static website on S3 with SSL (HTTPS) enabled, you do not need to rewrite everything from scratch.\nYou can refer to the detailed, step-by-step guide at the following link:\n👉 Step-by-step Guide: Setting up a Static Website with SSL on S3\nThis article will guide you through:\nCreating and configuring an S3 Bucket to host your static website. Setting up CloudFront as a CDN to accelerate and secure your website. Configuring AWS Certificate Manager (ACM) to provision free SSL certificates for your website. Connecting your custom domain using Route 53. Easily integrating full HTTPS support for your website. Note:\nYou do not need to repeat steps already detailed in the linked guide. Simply follow the instructions to successfully deploy a secure static website with SSL on AWS!\n"}, {"uri": "//localhost:1313/2-prerequiste/2.5-create-google-oauth2/", "title": "Create Google OAuth2 Project", "tags": [], "description": "", "content": "Create Google OAuth2 Project To integrate Google Sign-In functionality into your e-commerce website, you need to register and create a Google OAuth2 Project.\nSteps to follow:\nAccess Google Cloud Console\nOpen Google Cloud Console. Log in with your Google account. Create a New Project (if you don’t have one yet)\nClick Select a project &gt; New Project. Enter a project name, choose the location, and click Create. After the project is created, click Select a project. Select the newly created FcjFashionShop project. Enable Google OAuth2 API\nUnder Quick access, select API &amp; Services.\nOr, in the left menu, select APIs &amp; Services → Library. Type Google+ API in the search box and press Enter. Click the Google+ API service. Click Enable. At this preparation step, you only need to create the Google OAuth2 Project and enable the necessary APIs. Creating the OAuth Consent Screen and configuring the Client ID/Client Secret will be done in later steps, after you have your actual domain/frontend URL.\n"}, {"uri": "//localhost:1313/7-demo/", "title": "Demo and Run the Project", "tags": [], "description": "", "content": "Demo and Test the Project To give you a clear picture of the system&rsquo;s real-world operation, below is a demo video showcasing the full testing process for the completed dynamic e-commerce website. This video demonstrates all the main features, performed directly via the web interface.\nProject Demo Video Video demo dự án Features Demonstrated and Tested User registration Standard login Login with Google Admin creates product categories Admin creates brands Admin creates discount codes Dashboard/statistics (revenue, order/product count) Place orders Add to cart Checkout Export invoices The video records the complete feature testing process on the real website interface, ensuring the workflow and end-user experience. If you have questions about any specific operation, feel free to review the video or reach out for further clarification.\nConclusion:\nYou have just watched a real-world demo of a fully functional dynamic e-commerce website built on AWS Serverless. Every operation is validated through the video, guaranteeing transparency and effective implementation.\n"}, {"uri": "//localhost:1313/5-deployfrontend/5.2-enable-static-hosting/", "title": "Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions", "tags": [], "description": "", "content": "Enable Static Hosting, Configure CORS, and Grant Permissions for the S3 Bucket After uploading your frontend source code to the S3 Bucket, you need to enable static website hosting, set up the index and error documents, configure CORS policy, and grant public read permissions so your website runs stably, is accessible from browsers, and can connect to the backend API.\nStep 1: Enable Static Hosting and Configure CORS Enable static website hosting for the bucket\nGo to your frontend bucket in the AWS S3 Console. Select the Properties tab. Scroll down to Static website hosting and click Edit. Select Enable. Enter index.html in the Index document field. You may also enter index.html for the Error document field (recommended for SPA deployments). Save the settings. AWS will generate a Website endpoint like: http://fcjfashionshop.com.s3-website-ap-southeast-1.amazonaws.com Copy this URL for the next step. Configure CORS for the bucket\nGo to the Permissions tab → scroll down to CORS configuration → click Edit. Add the following sample configuration (JSON) to allow the frontend to access resources or upload images to S3 from other domains: [ { &#34;AllowedOrigins&#34;: [&#34;*&#34;], &#34;AllowedMethods&#34;: [&#34;GET&#34;, &#34;HEAD&#34;, &#34;PUT&#34;, &#34;POST&#34;, &#34;DELETE&#34;], &#34;AllowedHeaders&#34;: [&#34;*&#34;] } ] To restrict to a specific domain for better security: [ { &#34;AllowedOrigins&#34;: [&#34;https://your-domain.com&#34;], &#34;AllowedMethods&#34;: [&#34;GET&#34;, &#34;HEAD&#34;], &#34;AllowedHeaders&#34;: [&#34;*&#34;] } ] Click Save to apply. Step 2: Grant Public Permissions to the S3 Bucket Set permissions for the Frontend Bucket\nGo to the Permissions tab of the bucket, scroll down to Bucket policy → Edit. Add a policy to allow public read access, for example: { &#34;Version&#34;: &#34;2012-10-17&#34;, &#34;Statement&#34;: [ { &#34;Sid&#34;: &#34;PublicReadGetObject&#34;, &#34;Effect&#34;: &#34;Allow&#34;, &#34;Principal&#34;: &#34;*&#34;, &#34;Action&#34;: &#34;s3:GetObject&#34;, &#34;Resource&#34;: &#34;arn:aws:s3:::fcjfashionshop.com/*&#34; } ] } Click Save to apply. Set permissions for the avatar upload bucket (if any)\nGo to the Permissions tab of the bucket → Bucket policy. Add a policy to allow public read access, for example: { &#34;Version&#34;: &#34;2012-10-17&#34;, &#34;Statement&#34;: [ { &#34;Sid&#34;: &#34;PublicReadGetObject&#34;, &#34;Effect&#34;: &#34;Allow&#34;, &#34;Principal&#34;: &#34;*&#34;, &#34;Action&#34;: &#34;s3:GetObject&#34;, &#34;Resource&#34;: &#34;arn:aws:s3:::uploads-avatars-2025/*&#34; } ] } Click Save to apply. Step 3: Verify Website Operation Use the Website endpoint link to test your website. If index.html loads successfully, your website is running on S3. If you encounter 403/404 errors: Double-check your bucket policy permissions. Make sure static hosting is enabled. Verify that your index/error file names are correct. Note:\nYou should only use &quot;*&quot; for development/testing. When deploying to production, always specify your real website domain in AllowedOrigins for better security.\nConclusion:\nProperly enabling static hosting, configuring CORS, and setting public permissions will ensure your frontend website on S3 operates reliably, can call the backend API, and provides optimal load speed and user experience.\n"}, {"uri": "//localhost:1313/8-cleanup/", "title": "Clean Up Resources", "tags": [], "description": "", "content": "Clean Up Resources After completing the demo and project experience, you should clean up your AWS resources to avoid incurring unnecessary charges.\nIt is recommended to delete S3 Buckets before deleting the CloudFormation Stack to ensure the stack is deleted successfully!\n1. Delete S3 Buckets for Frontend and Upload Files Go to the AWS S3 Console.\nSelect each bucket you created for the frontend and avatar uploads (e.g., fcjshop-frontend-website, uploads-avatars-2025).\nClick Empty.\nSelect fcjshop-frontend-website again.\nClick Delete.\nDo the same for uploads-avatars-2025. 2. Delete the Stack via CloudFormation Go to the CloudFormation Console.\nSelect the stack you deployed (e.g., fcjfashioshop.com or your stack&rsquo;s name).\nClick Delete. Confirm the delete action. Repeat for any other Stacks you deployed.\nNote:\nAfter deleting the CloudFormation stack, all backend resources such as Lambda, API Gateway, DynamoDB tables, IAM roles, etc. will be automatically removed.\nConclusion:\nYou have cleaned up all AWS resources used for this project, ensuring there are no unwanted charges. If you want to try again, simply redeploy from scratch!\nCheck the AWS Billing page to ensure there are no remaining chargeable services.\n"}, {"uri": "//localhost:1313/5-deployfrontend/5.3-s3-bucket-permission/", "title": "Grant Public Permissions to S3 Bucket", "tags": [], "description": "", "content": "Grant Public Permissions to S3 Bucket To access your website interface, you need to configure the permissions for the S3 Bucket that hosts your frontend.\nStep 1: Grant Public Permissions to the S3 Bucket Grant permissions for the Frontend Bucket\nGo to the Permissions tab of your bucket, scroll down to Bucket policy → Edit. Add a policy to allow public read access to files, for example: { &#34;Version&#34;: &#34;2012-10-17&#34;, &#34;Statement&#34;: [ { &#34;Sid&#34;: &#34;PublicReadGetObject&#34;, &#34;Effect&#34;: &#34;Allow&#34;, &#34;Principal&#34;: &#34;*&#34;, &#34;Action&#34;: &#34;s3:GetObject&#34;, &#34;Resource&#34;: &#34;arn:aws:s3:::fcjfashionshop.com/*&#34; } ] } Click Save to apply. Grant permissions for the avatar upload bucket (if any)\nGo to the Permissions tab of the bucket → Bucket policy. Add a policy to allow public read access to files, for example: { &#34;Version&#34;: &#34;2012-10-17&#34;, &#34;Statement&#34;: [ { &#34;Sid&#34;: &#34;PublicReadGetObject&#34;, &#34;Effect&#34;: &#34;Allow&#34;, &#34;Principal&#34;: &#34;*&#34;, &#34;Action&#34;: &#34;s3:GetObject&#34;, &#34;Resource&#34;: &#34;arn:aws:s3:::uploads-avatars-2025/*&#34; } ] } Click Save to apply. Step 2: Verify Website Operation Use the Website endpoint link to test your website. If index.html loads successfully, your website is running on S3. If you encounter 403/404 errors: Double-check your bucket policy permissions. Make sure static hosting is enabled. Verify that your index/error file names are correct. Note:\nOnly use &quot;*&quot; for development/testing. When deploying to production, always specify your actual website domain in AllowedOrigins for enhanced security.\n"}, {"uri": "//localhost:1313/5-deployfrontend/5.4-clientid-clientserver/", "title": "Configure Google OAuth2 Client ID and Client Secret", "tags": [], "description": "", "content": "Configure Google OAuth2 Client ID and Client Secret After creating the Google OAuth2 Project and enabling the required APIs during the environment preparation step, you need to create an OAuth2 Client ID and Client Secret to integrate Google Sign-In into your e-commerce system.\nSteps to follow:\nAccess Google Cloud Console\nOpen Google Cloud Console and select the correct project you created earlier. Create OAuth2 Client ID\nNavigate to APIs &amp; Services → Credentials. Click + Create Credentials → OAuth client ID. Click Configure consent screen. Click Get Started. Fill in the following information:\nApp name: FcjFashionShop\nUser support email: Enter your email\nClick Next Email addresses: Enter your email\nClick Next Check I agree to the Google API Services: User Data Policy.\nClick Continue and then Create In Metrics, click Create OAuth client For Application type, select Web application\nName: FcjFashionShop In Authorized JavaScript origins\nClick Add URI to add a new URL Paste your S3 Bucket website endpoint (the static website URL you copied earlier) In Authorized redirect URIs\nClick Add URI to add a new URL Paste the Invoke URL of your API Gateway that you copied earlier, replacing your-API-Gateway-domain in the example below Click Create your-API-Gateway-domain/api/users/auth/google/callback ClientID and ClientSecret have been created successfully. Copy and save them for later use. Authorized JavaScript origins: this is your frontend domain (the S3 Static Website endpoint).\nAuthorized redirect URIs: this is the backend endpoint (API Gateway) that handles the Google callback.\nSecurity note:\nNever publish your Client Secret on Github or anywhere public!\nConclusion:\nAfter completing these steps, you have all the necessary information to configure Google OAuth2 for both backend and frontend, and are ready to implement Google Sign-In for your e-commerce website project on AWS.\n"}, {"uri": "//localhost:1313/5-deployfrontend/5.5-connect-frontend-api-backend/", "title": "Connect the Frontend to the Backend API", "tags": [], "description": "", "content": "Connect the Frontend to the Backend API After uploading your website to S3 and successfully enabling static hosting, the final step is to connect the frontend to the backend through the API Gateway endpoint that you previously tested. This step is crucial for your website to fetch/send dynamic data and function as a modern e-commerce application.\nSteps to follow:\nIdentify the Backend API Endpoint\nUse the API Gateway endpoint obtained in the backend testing step.\nFor example: https://ars3538v1i.execute-api.ap-southeast-1.amazonaws.com/Prod/api/users Update the Endpoint Configuration in the Frontend Source Code\nOpen your frontend project folder. Open the .env file in the Frontend directory. Open the index.js file in the Frontend directory. Paste the copied API Gateway URL into the base URL variable in your code. Conclusion:\nOnce you have successfully connected the frontend to the backend, your website is ready to operate dynamically with real data from AWS. You can proceed to configure a custom domain, enable SSL security, or deploy CloudFront for further optimization.\n"}, {"uri": "//localhost:1313/", "title": "Dynamic E-Commerce Website", "tags": [], "description": "", "content": "Building a Dynamic E-Commerce Website with AWS Serverless Overview In this workshop, you will deploy a dynamic e-commerce website using the Hugo framework on the AWS Cloud platform. You will learn how to prepare your environment, configure your AWS account, build the website with <PERSON>, and deploy the entire system using essential AWS services such as API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, and leverage SAM CLI to automate the deployment process.\nAdditionally, you will be guided through setting up a static website on S3 with full SSL (HTTPS) support to enhance security and professionalism, ensuring your website serves customers globally with confidence.\nWorkshop Objectives Gain proficiency in essential AWS tools required for a dynamic website project. Learn to prepare, install, and configure the development environment for a Hugo project. Build, package, and deploy a dynamic website with <PERSON>, integrating modern AWS serverless services. Design and deploy APIs with API Gateway, implement business logic using Lambda, store data with DynamoDB, and manage your dynamic website on S3. Use CloudFormation to automate AWS resource creation and configuration; monitor and observe system activities with CloudWatch. Set up a static website on S3 integrated with SSL (HTTPS) using AWS Certificate Manager and CloudFront. Configure custom domains and DNS resolution with Route 53 for public internet access to your dynamic website. Apply DevOps workflows to automate deployment and operations of your website efficiently on AWS. What You Will Learn from This Workshop After completing this workshop, you will:\nClearly understand the architecture and deployment workflow of a dynamic e-commerce website on AWS. Learn to use AWS services such as API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, and SAM CLI in a real-world project. Know how to build, package, and deploy a dynamic website with Hugo, connecting the frontend with the backend via API Gateway and Lambda. Be able to set up a static website on S3, use CloudFront and AWS Certificate Manager to enable HTTPS for your website. Practice managing dynamic data with DynamoDB and automate infrastructure with CloudFormation. Configure custom domains with Route 53, monitor your system, and analyze application logs via CloudWatch. Be ready to apply these skills to real-world projects, dynamic websites, serverless architectures, or DevOps workflows on AWS. Contents Introduction Preparation Steps Deploy Backend Test Backend API with Postman Deploy Frontend Setup SSL S3 Static Website Demo and Project Run Clean Up Resources "}, {"uri": "//localhost:1313/categories/", "title": "Categories", "tags": [], "description": "", "content": ""}, {"uri": "//localhost:1313/tags/", "title": "Tags", "tags": [], "description": "", "content": ""}]