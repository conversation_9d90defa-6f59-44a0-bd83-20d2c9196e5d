[{"uri": "//localhost:1313/1-introduce/", "title": "Introduction", "tags": [], "description": "", "content": "System Introduction This workshop will guide you to build and deploy a dynamic e-commerce website using modern AWS serverless services. You will use key components such as API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, and SAM CLI to automate, scale, and optimize the cost of your system.\nOverall Architecture Below is the overall architecture of the system.\n(Insert architecture diagram here)\nFrontend: Uses Hugo theme, hosted statically on Amazon S3 and delivered through CloudFront. Backend: Communicates via RESTful APIs with API Gateway, business logic is handled by AWS Lambda. Database: Uses DynamoDB for dynamic data storage, easily scalable. Infrastructure Management: CloudFormation automatically creates &amp; configures AWS resources. SAM CLI is used for serverless deployment and automation. Domain &amp; Operation Management: Route 53 manages domain/DNS. CloudWatch is used to monitor, log, and provide alerts for the whole system. Data Flow Diagram The following diagram illustrates the main data flow and interactions between system components.\n(Insert data flow or sequence diagram here)\nList of AWS Services Used API Gateway: Creates REST APIs for communication between frontend and backend. S3: Stores the static website and website assets. Lambda: Handles serverless backend logic. DynamoDB: The database. CloudFormation: Automated infrastructure management. Route 53: Domain registration and DNS management. CloudWatch: System monitoring and alerting, supports effective debugging/tracking. SAM CLI: Automates the deployment of serverless resources. Main Features of the Website Fully serverless architecture on AWS. Deploy a dynamic, secure, and automatically scalable website. User-friendly interface with Hugo. Dynamic backend using RESTful APIs via API Gateway &amp; Lambda. Dynamic database with DynamoDB. Real-time data processing with Lambda &amp; DynamoDB. Integrated monitoring, logging, and DNS configuration. Infrastructure as code with CloudFormation &amp; SAM CLI. "}, {"uri": "//localhost:1313/2-prerequiste/", "title": "Preparation", "tags": [], "description": "", "content": "Overview of &ldquo;Preparation&rdquo; Before you begin deploying your dynamic e-commerce website using AWS services like API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, and SAM CLI, you need to complete a few basic environment preparation steps. These tools and services will help you quickly, securely, and efficiently deploy your application.\nIn this section, you will perform the necessary setup steps for both the frontend and backend environments of the project:\nInstall Hugo: To build and deploy the frontend of the website. Install NodeJS and Yarn: To support frontend build and management. Install SAM CLI: To deploy the serverless backend on AWS. Create AWS account and configure IAM: To set up access and security for the AWS environment. These preparation steps will ensure that you have the proper development environment in place and working correctly before proceeding with deploying AWS services for your project.\n⚠️ Note: Make sure you have all the tools installed before starting to work with AWS services. Otherwise, you may encounter issues during deployment.\nContent Install NodeJS Install Yarn for frontend Install SAM CLI for backend Create AWS Account &amp; Configure IAM Create Google OAuth2 Project /)\n"}, {"uri": "//localhost:1313/3-tri%E1%BB%83n-khai-backend/", "title": "Deploying the Backend", "tags": [], "description": "", "content": "Deploying the backend for your dynamic e-commerce website is accomplished using the AWS SAM CLI in combination with CloudFormation. With these tools, you can easily deploy serverless services such as API Gateway, Lambda, and DynamoDB.\nMain Tools Used: SAM CLI: A command-line tool for developing, testing, and deploying serverless applications. SAM CLI makes it easy to deploy API Gateway, Lambda functions, and other serverless resources on AWS. CloudFormation: An infrastructure as code service that allows you to automate the deployment and management of AWS resources. In this section, you will learn how to deploy a serverless backend for your website using SAM CLI, including the creation of resources such as API Gateway, Lambda, and DynamoDB.\nNote: Please ensure you have completed the environment preparation steps (including installing the SAM CLI and configuring AWS IAM) before starting the backend deployment.\nContents Deploying the backend with SAM CLI/CloudFormation Managing backend resources with CloudFormation/SAM CLI "}, {"uri": "//localhost:1313/2-prerequiste/2.1-installnodejs/", "title": "Installing NodeJS", "tags": [], "description": "", "content": "NodeJS is a powerful and popular JavaScript runtime environment, widely used for developing dynamic web applications. In this step, you will install NodeJS on your operating system to build your dynamic e-commerce website.\nStep 1: Install NodeJS Download NodeJS from the official website:\nVisit the official NodeJS website at Node.js and download the LTS version for your operating system (Windows, macOS, or Linux). Download the .msi file for Windows, .pkg for macOS, or .tar.xz for Linux. Install NodeJS on Windows:\nAfter downloading the .msi file, open it and follow the installation instructions. Make sure to check Add to PATH during the installation process. Install NodeJS on macOS:\nInstall via Homebrew: brew install node Install NodeJS on Linux (Ubuntu):\nUse the following command to install NodeJS:\nsudo apt install nodejs\rVerify the installation:\nAfter installation, open your terminal (or Command Prompt on Windows) and run the following command to check the NodeJS version:\nnode -v\rIf the installation is successful, you will see the NodeJS version displayed.\nExample\nAfter installation, open your terminal (or Command Prompt on Windows) and enter the following command to check the NodeJS version:\nC:\\Users\\<USER>"}, {"uri": "//localhost:1313/3-tri%E1%BB%83n-khai-backend/3.1-deploy-backend/", "title": "Connect to Public Instance", "tags": [], "description": "", "content": "\nGo to EC2 service management console. Click on Public Linux Instance. Click Actions. Click Security. Click Modify IAM role. At the Modify IAM role page. Click to select SSM-Role. Click Save. You will need to wait about 10 minutes before performing the next step. This time our EC2 instance will automatically register with the Session Manager.\nGo to the AWS Systems Manager service management console Drag the left menu slider down. Click Session Manager. Click Start Session. Then select Public Linux Instance and click Start session to access the instance. Terminal will appear on the browser. Testing with the command sudo tcpdump -nn port 22 and sudo tcpdump we will see no SSH traffic but only HTTPS traffic. Above, we have created a connection to the public instance without opening SSH port 22, for better security, avoiding any attack to the SSH port.\nOne disadvantage of the above method is that we have to open the Security Group outbound at port 443 to the internet. Since it&rsquo;s a public instance, it probably won&rsquo;t be a problem, but if you want extra security, you can block port 443 to the internet and still use the Session Manager. We will go through this in the private instance section below.\nYou can click terminate to end the currently connected session before proceeding to the next step.\n"}, {"uri": "//localhost:1313/2-prerequiste/2.2-installyarn/", "title": "Installing Yarn", "tags": [], "description": "", "content": "Yarn is a package manager for JavaScript that is widely used in frontend projects to efficiently manage libraries and software packages. Yarn helps address issues related to installation speed, consistency, and security when managing project dependencies.\nStep 1: Install Yarn Install Yarn via npm:\nYarn can be installed through npm (Node Package Manager). Make sure you have NodeJS and npm installed before proceeding.\nTo install Yarn, open your terminal or Command Prompt and run the following command:\nnpm install -g yarn\rInstall Yarn on macOS (via Homebrew):\nIf you are using macOS, you can install Yarn via Homebrew with the following command:\nbrew install yarn\rInstall Yarn on Linux (Ubuntu):\nTo install Yarn on Ubuntu, you need to add the Yarn repository to your system and run the following command:\nsudo apt update &amp;&amp; sudo apt install yarn\rYarn has been installed successfully! You can now start managing frontend libraries for your project.\nStep 2: Verify Yarn Installation Check Yarn version:\nAfter successfully installing Yarn, you can verify the version by running:\nyarn --version\rIf the version number is displayed, Yarn has been installed successfully:\nC:\\Users\\<USER>"}, {"uri": "//localhost:1313/4-testbackendapi/", "title": "Testing Backend APIs with Postman", "tags": [], "description": "", "content": "Testing Backend APIs with Postman After successfully deploying your backend with API Gateway and Lambda, the next step is to test your APIs to verify that the backend system is functioning correctly before integrating with the frontend.\nPostman is a powerful and widely-used tool that allows you to send requests (GET, POST, PUT, DELETE, &hellip;) to API endpoints, view responses, verify processing logic, and debug errors.\nYou can download Postman here: Download Postman\nObjectives of this section:\nGuide you on how to retrieve the API Gateway endpoint that you just deployed on AWS to use with Postman. Practice sending GET/POST requests to the API, analyze the returned responses to verify backend correctness. Identify and troubleshoot common errors encountered during API testing. This section covers:\nRetrieving the API Gateway endpoint:\nInstructions on how to locate and copy the endpoint URL from the AWS Console for API testing purposes. Sending GET/POST requests to verify backend responses:\nStep-by-step guidance on how to send GET/POST requests with Postman, enter payload data, and review backend responses. Note:\nBefore testing APIs with Postman, make sure that the backend has been deployed successfully and there are no errors from previous steps (CloudFormation/Lambda must be in a successful state).\nBy the end of this section, you will:\nKnow how to obtain the API Gateway endpoint for testing. Be proficient in using Postman to send GET/POST requests to your backend. Be able to analyze and verify API responses, and be ready to connect your tested backend with the frontend. Contents Retrieve the API Gateway endpoint Send GET/POST requests to verify backend responses "}, {"uri": "//localhost:1313/testchaper/example/", "title": "Example", "tags": [], "description": "", "content": "Chapter X Some Chapter title Lorem Ipsum.\n"}, {"uri": "//localhost:1313/testchaper/", "title": "TestChaper", "tags": [], "description": "", "content": "Chapter X Some Chapter title Lorem Ipsum.\n"}, {"uri": "//localhost:1313/3-tri%E1%BB%83n-khai-backend/3.2-check-status-log-backend/", "title": "Checking Backend Status and Logs After Deployment", "tags": [], "description": "", "content": "Checking Backend Status and Logs After Deployment After deploying your backend on AWS using SAM CLI and CloudFormation, the next crucial step is to check the resource status and review backend logs to ensure all components are functioning correctly.\nMake sure that API Gateway, Lambda, DynamoDB, etc. have been created successfully and are running. Quickly identify and resolve deployment or logic errors if any issues occur. Prepare for API testing using Postman or your frontend application. Check CloudFormation Stack and Resource Status\nAccess the AWS CloudFormation Console. Select the recently deployed Stack (e.g., sam-app). In the Status tab, if the status is CREATE_COMPLETE or UPDATE_COMPLETE, your deployment was successful. In the Resources tab, view the list of created resources (API Gateway, Lambda, DynamoDB, S3, etc.). If you see a status like ROLLBACK or FAILED, click on the Events tab to view error details and identify the root cause.\nView Lambda Logs with CloudWatch\nIn the AWS Console, go to Lambda → select your deployed function. Scroll down and click on the Monitor tab, then select View CloudWatch logs to access detailed logs. Scroll down and click on the Log streams tab, then select the first log stream. Each time a request is made (GET/POST, etc.), <PERSON><PERSON> writes logs to CloudWatch. Here you can:\nView received requests. Check returned responses. Debug issues if the code crashes or returns a 500 error. Verify API Gateway Functionality\nIn the AWS Console, go to API Gateway → select the API you just created. Go to Stages → select the relevant stage → choose prod.\nCopy the Invoke URL to prepare for testing with Postman/curl in the next step. Once you&rsquo;ve verified that all resources are functioning properly and there are no critical errors in the logs, you&rsquo;re ready to test your backend API using Postman or integrate with your frontend!\n"}, {"uri": "//localhost:1313/5-deployfrontend/", "title": "Deploying the Frontend", "tags": [], "description": "", "content": "Deploying the Frontend After the backend is ready and has been successfully tested, the next step is to deploy the frontend of your website to AWS S3 to create a static website, optimizing both costs and page loading speed.\nAt the same time, you will configure hosting, caching, and CORS so that the frontend operates smoothly and securely when connecting to the backend API.\nObjectives of this section:\nGuide you through uploading and deploying frontend code to an AWS S3 Bucket. Enable static website hosting, and configure cache and CORS for stable and secure frontend access. Connect the frontend to the tested backend API endpoint from previous steps. Note:\nMake sure you have yarn installed in your project directory.\nBy the end of this section, you will:\nHave successfully deployed the frontend website on S3, making it publicly accessible to everyone. Master hosting, caching, and CORS configuration—ensuring your website loads quickly, safely, and is compatible with the backend. Be ready to upgrade the system with a custom domain, CloudFront, or new features in the next steps. Contents Deploy Frontend to S3 Bucket Enable static hosting, configure CORS Configure Google OAuth2 Client ID and Client Secret Connect the frontend to the backend API "}, {"uri": "//localhost:1313/2-prerequiste/2.3-installsamcli/", "title": "Installing SAM CLI", "tags": [], "description": "", "content": "The AWS SAM CLI is a command-line tool that helps you develop, package, and deploy serverless applications using AWS Lambda, API Gateway, and other AWS serverless services. In this step, you will install the SAM CLI to develop the serverless backend for your dynamic e-commerce website.\nStep 1: Install the SAM CLI Install on Windows:\nGo to the Installing the AWS SAM CLI page to download the SAM CLI installer for Windows. Select Windows. Run the .msi installer and follow the instructions to complete the installation. Open your terminal and enter sam --version. Install on macOS:\nUse Homebrew to install the SAM CLI:\nbrew tap aws/tap\rbrew install aws-sam-cli\rInstall on Linux (Ubuntu):\nOn Linux, you can use apt to install the SAM CLI:\nsudo apt-get update\rsudo apt-get install aws-sam-cli\rInstall SAM CLI from source (if needed):\nIf you prefer not to use the methods above, you can install the SAM CLI from source. Detailed instructions can be found at: Install SAM CLI from source. The SAM CLI has been installed successfully! You can now start developing and deploying serverless applications with AWS.\nStep 2: Verify SAM CLI Installation Check the SAM CLI version:\nAfter a successful installation, open your terminal (or Command Prompt on Windows) and enter the following command to check the SAM CLI version:\nsam --version\rVerify SAM CLI installation:\nIf the above command displays the version information of the SAM CLI, the tool has been installed successfully.\nC:\\Users\\<USER>"}, {"uri": "//localhost:1313/4-testbackendapi/4.1-api-gateway-endpoint/", "title": "Retrieving the API Gateway Endpoint", "tags": [], "description": "", "content": "Retrieving the API Gateway Endpoint After deploying the backend using SAM CLI/CloudFormation, the system will automatically generate an endpoint address for your API Gateway. This is the address you will use to send test requests via Postman, curl, or integrate with your frontend later.\nNote:\nUse the previously saved endpoint if you already have it.\nSteps to follow:\nAccess the AWS Management Console\nLog in to the AWS Console. Select API Gateway from the list of AWS services. Select the Recently Deployed API\nIn the API Gateway interface, you&rsquo;ll see a list of existing APIs. Find and click on the name of the API you just deployed. Retrieve the Endpoint from Stages\nIn the left-hand menu, select Stages. Click on the stage you have deployed (e.g., prod). At the top, you&rsquo;ll see the Invoke URL. This is the API Gateway endpoint you will use for testing. Copy this URL. Verify the Endpoint\nYou can copy this endpoint and paste it into your browser or Postman to check the basic response. Typically, if you haven&rsquo;t configured specific resources (e.g., /products), accessing the root endpoint will return a “Missing Authentication Token” error or HTTP 403. This is normal—just make sure to append the correct resource route when testing your API. Tip:\nIt is recommended to save this endpoint in your project README or personal notes to avoid having to revisit the AWS Console multiple times during API testing.\n"}, {"uri": "//localhost:1313/2-prerequiste/2.4-createiam/", "title": "Create Account and Configure IAM", "tags": [], "description": "", "content": "Create Account and Configure IAM AWS Identity and Access Management (IAM) allows you to securely manage access to AWS resources. In this step, you will create an IAM user, grant appropriate permissions, and configure an Access Key for use in subsequent steps of the workshop.\nCreate IAM User Go to the AWS Management Console and navigate to the IAM service.\nIn the left navigation pane, select Users and click Create users from the left menu.\nCreate the IAM user:\nSet the User name as Admin<PERSON>ser (or any name you prefer). Check Provide user access to the AWS Management Console – optional. Under User type, select I want to create an IAM user. Choose Custom password – enter your password. Uncheck Users must create a new password at next sign-in – Recommended. Click Next. Grant permissions to the user:\nSelect Attach policies directly. Search for and select the following policies: AWSCloudFormationFullAccess, AmazonDynamoDBFullAccess, AWSLambda_FullAccess, AmazonS3FullAccess, AmazonAPIGatewayAdministrator, IAMFullAcces to grant the necessary project permissions. Scroll down and click Next. Review the information on the Permissions summary page.\nClick Create user to create the AWS IAM account. The IAM user has been created successfully.\nClick Download .csv file to save the account credentials. Click View user to see detailed information. Create Access Key Display and save the Access Key and Secret Access Key:\nClick Create access key to generate an access key for the user. Check Command Line Interface (CLI) and click Next. Click Show to reveal the Secret access key value. Click Create access key. Click Download .csv file to store the credentials. Then click Done. "}, {"uri": "//localhost:1313/5-deployfrontend/5.1-frontend-s3/", "title": "Deploying the Frontend to S3 Bucket", "tags": [], "description": "", "content": "Deploying the Frontend to S3 Bucket To bring your frontend website online, you will upload all the built source files to AWS S3 Bucket. S3 acts as a static web host, allowing you to serve your website with low cost and high speed.\nSteps to follow:\nBuild the frontend source code\nOpen the terminal in VSCode and navigate to the frontend directory. Copy and paste the following command into the terminal: yarn build The build folder will contain all the static files to upload. Wait about 1 minute for the build process to complete. Create a new S3 Bucket on AWS\nGo to the AWS S3 Console. Click Create bucket. Enter a bucket name, e.g., my-frontend-bucket-2025. Region: Choose the same region as your backend (recommended: ap-southeast-1 for consistency). Disable “Block all public access”. Check I acknowledge that the current settings might result in this bucket and the objects within becoming public. to confirm the warning so your website can be public. Click Create bucket to finish. Caution:\nA bucket hosting a static website must have public read access for everyone. However, do NOT upload any sensitive data here because anyone with the link can access it!\nGrant public read permissions to the bucket\nAfter creating the bucket, proceed to grant public access. Click the bucket you just created. Go to the Permissions tab of the bucket. Scroll down to find Bucket policy. Click Edit. Add a policy to allow public read access, for example:\n{ &#34;Version&#34;: &#34;2012-10-17&#34;, &#34;Statement&#34;: [ { &#34;Sid&#34;: &#34;PublicReadGetObject&#34;, &#34;Effect&#34;: &#34;Allow&#34;, &#34;Principal&#34;: &#34;*&#34;, &#34;Action&#34;: &#34;s3:GetObject&#34;, &#34;Resource&#34;: &#34;arn:aws:s3:::my-frontend-bucket-2025/*&#34; } ] } Click Save to apply.\nUpload the built source code to the bucket using AWS CLI\nEnsure you have installed and configured AWS CLI (run aws configure). Run the following commands: cd frontend\raws s3 cp build/ s3://my-frontend-bucket-2025/ --recursive The --recursive flag uploads all files and subfolders inside the build directory. Verify bucket contents\nGo back to the bucket on AWS Console and confirm that all files (index.html, main.js, CSS, images, etc.) have been uploaded successfully.\nYou can click directly on a file (e.g., index.html) and copy the Object URL to check if the file is publicly accessible (you should be able to view the HTML/raw file in your browser).\nYou can also check all files and folders uploaded to S3 by browsing the Objects section.\nConclusion:\nAfter completing these steps, your frontend website is now uploaded to AWS S3 and ready for enabling static hosting in the next step.\n"}, {"uri": "//localhost:1313/4-testbackendapi/4.2-get-post-response/", "title": "Sending GET/POST Requests to Verify Backend Responses", "tags": [], "description": "", "content": "Sending GET/POST Requests to Verify Backend Login Responses After obtaining the API Gateway endpoint, you need to send POST and GET requests to the API to confirm that the backend is functioning properly. This is typically done using Postman, the most popular API testing tool.\nSteps to follow:\nTesting POST Method with Postman\nOpen Postman, select New → HTTP Request.\nSet the method to POST.\nEnter the full endpoint URL, for example:\nhttps://ars3538v1i.execute-api.ap-southeast-1.amazonaws.com/Prod/api/users Switch to the Body tab → select raw → choose JSON. Enter sample data in the body as follows:\n{ &#34;username&#34;: &#34;AdminUser&#34;, &#34;email&#34;: &#34;<EMAIL>&#34;, &#34;password&#34;: &#34;AdminUser123@&#34;, &#34;firstName&#34;: &#34;Admin&#34;, &#34;lastName&#34;: &#34;User&#34;, &#34;isAdmin&#34;: true, &#34;googleId&#34;: null, &#34;avatar&#34;: &#34;https://i.imgur.com/avatar.jpg&#34;, &#34;phone&#34;: &#34;0912345678&#34;, &#34;address&#34;: &#34;&#34;, &#34;isDeleted&#34;: false } Click Send to submit the request.\nIf the API works correctly, you will receive a confirmation response.\nCopy the returned token to use for subsequent requests. If the API requires authentication (API Key, Bearer Token, etc.) or custom headers, add them in the Headers tab of Postman before sending the request!\nTesting GET Method for Login with Postman\nGo to DynamoDB on the AWS Console.\nSelect the ShopUser table. Choose Explore table items to check the data for the newly created user. Scroll down and edit the isAdmin field for the created user to true. Create a new request in Postman, set the method to GET.\nEnter the full endpoint URL, for example:\nhttps://ars3538v1i.execute-api.ap-southeast-1.amazonaws.com/Prod/api/users In the Authorization section, choose: Bearer Token.\nIn the Token field: paste the previously saved token.\nClick Send to submit the request. If the API is functioning correctly, you will receive a response with the user data from the database.\nCheck the HTTP status code (should be 200 OK) and the response body. You can test other project functionalities by reviewing the code to find the respective API URLs.\nAnalyzing Results and Handling Common Errors\nIf you receive the correct response data → the backend is working as expected. If you encounter errors like 403 Forbidden, 401 Unauthorized, or Missing Authentication Token: Verify the endpoint and ensure the correct resource route is used. Check the API Gateway configuration and mapping templates. Review the Lambda logs in CloudWatch for detailed error information. If you receive a 500 Internal Server Error: This is often due to an error in the Lambda code. Go to CloudWatch logs to debug. Conclusion:\nTesting the API using POST/GET requests is an essential step to confirm that the backend is functioning correctly before integrating with the frontend or deploying to production. If errors occur, check CloudWatch logs or review the API Gateway and Lambda configurations for timely troubleshooting.\n"}, {"uri": "//localhost:1313/2-prerequiste/2.5-create-google-oauth2/", "title": "Create Google OAuth2 Project", "tags": [], "description": "", "content": "Create Google OAuth2 Project To integrate Google Sign-In functionality into your e-commerce website, you need to register and create a Google OAuth2 Project.\nSteps to follow:\nAccess Google Cloud Console\nOpen Google Cloud Console. Log in with your Google account. Create a New Project (if you don’t have one yet)\nClick Select a project &gt; New Project. Enter a project name, choose the location, and click Create. After the project is created, click Select a project. Select the newly created FcjFashionShop project. Enable Google OAuth2 API\nUnder Quick access, select API &amp; Services.\nOr, in the left menu, select APIs &amp; Services → Library. Type Google+ API in the search box and press Enter. Click the Google+ API service. Click Enable. At this preparation step, you only need to create the Google OAuth2 Project and enable the necessary APIs. Creating the OAuth Consent Screen and configuring the Client ID/Client Secret will be done in later steps, after you have your actual domain/frontend URL.\n"}, {"uri": "//localhost:1313/5-deployfrontend/5.2-enable-static-hosting/", "title": "Deploying the Frontend to S3 Bucket", "tags": [], "description": "", "content": "Deploying the Frontend to S3 Bucket To bring your frontend website online, you will upload all the built source files to AWS S3 Bucket. S3 acts as a static web host, allowing you to serve your website with low cost and high speed.\nSteps to follow:\nBuild the frontend source code\nOpen the terminal in VSCode and navigate to the frontend directory. Copy and paste the following command into the terminal: yarn build The build folder will contain all the static files to upload. Wait about 1 minute for the build process to complete. Create a new S3 Bucket on AWS\nGo to the AWS S3 Console. Click Create bucket. Enter a bucket name, e.g., my-frontend-bucket-2025. Region: Choose the same region as your backend (recommended: ap-southeast-1 for consistency). Disable “Block all public access”. Check I acknowledge that the current settings might result in this bucket and the objects within becoming public. to confirm the warning so your website can be public. Click Create bucket to finish. Caution:\nA bucket hosting a static website must have public read access for everyone. However, do NOT upload any sensitive data here because anyone with the link can access it!\nGrant public read permissions to the bucket\nAfter creating the bucket, proceed to grant public access. Click the bucket you just created. Go to the Permissions tab of the bucket. Scroll down to find Bucket policy. Click Edit. Add a policy to allow public read access, for example:\n{ &#34;Version&#34;: &#34;2012-10-17&#34;, &#34;Statement&#34;: [ { &#34;Sid&#34;: &#34;PublicReadGetObject&#34;, &#34;Effect&#34;: &#34;Allow&#34;, &#34;Principal&#34;: &#34;*&#34;, &#34;Action&#34;: &#34;s3:GetObject&#34;, &#34;Resource&#34;: &#34;arn:aws:s3:::my-frontend-bucket-2025/*&#34; } ] } Click Save to apply.\nUpload the built source code to the bucket using AWS CLI\nEnsure you have installed and configured AWS CLI (run aws configure). Run the following commands: cd frontend\raws s3 cp build/ s3://my-frontend-bucket-2025/ --recursive The --recursive flag uploads all files and subfolders inside the build directory. Verify bucket contents\nGo back to the bucket on AWS Console and confirm that all files (index.html, main.js, CSS, images, etc.) have been uploaded successfully.\nYou can click directly on a file (e.g., index.html) and copy the Object URL to check if the file is publicly accessible (you should be able to view the HTML/raw file in your browser).\nYou can also check all files and folders uploaded to S3 by browsing the Objects section.\nConclusion:\nAfter completing these steps, your frontend website is now uploaded to AWS S3 and ready for enabling static hosting in the next step.\n"}, {"uri": "//localhost:1313/5-deployfrontend/5.3-clientid-clientserver/", "title": "Configure Google OAuth2 Client ID and Client Secret", "tags": [], "description": "", "content": "Configure Google OAuth2 Client ID and Client Secret After creating the Google OAuth2 Project and enabling the required APIs during the environment preparation step, you need to create an OAuth2 Client ID and Client Secret to integrate Google Sign-In into your e-commerce system.\nSteps to follow:\nAccess Google Cloud Console\nOpen Google Cloud Console and select the correct project you created earlier. Create OAuth2 Client ID\nNavigate to APIs &amp; Services → Credentials. Click + Create Credentials → OAuth client ID. Click Configure consent screen. Click Get Started. Fill in the following information:\nApp name: FcjFashionShop\nUser support email: Enter your email\nClick Next Email addresses: Enter your email\nClick Next Check I agree to the Google API Services: User Data Policy.\nClick Continue and then Create In Metrics, click Create OAuth client For Application type, select Web application\nName: FcjFashionShop In Authorized JavaScript origins\nClick Add URI to add a new URL Paste your S3 Bucket website endpoint (the static website URL you copied earlier) In Authorized redirect URIs\nClick Add URI to add a new URL Paste the Invoke URL of your API Gateway that you copied earlier, replacing your-API-Gateway-domain in the example below Click Create your-API-Gateway-domain/api/users/auth/google/callback ClientID and ClientSecret have been created successfully. Copy and save them for later use. Authorized JavaScript origins: this is your frontend domain (the S3 Static Website endpoint).\nAuthorized redirect URIs: this is the backend endpoint (API Gateway) that handles the Google callback.\nSecurity note:\nNever publish your Client Secret on Github or anywhere public!\nConclusion:\nAfter completing these steps, you have all the necessary information to configure Google OAuth2 for both backend and frontend, and are ready to implement Google Sign-In for your e-commerce website project on AWS.\n"}, {"uri": "//localhost:1313/6-cleanup/", "title": "Clean up resources", "tags": [], "description": "", "content": "We will take the following steps to delete the resources we created in this exercise.\nDelete EC2 instance Go to EC2 service management console\nClick Instances. Select both Public Linux Instance and Private Windows Instance instances. Click Instance state. Click Terminate instance, then click Terminate to confirm. Go to IAM service management console\nClick Roles. In the search box, enter SSM. Click to select SSM-Role. Click Delete, then enter the role name SSM-Role and click Delete to delete the role. Click Users. Click on user Portfwd. Click Delete, then enter the user name Portfwd and click Delete to delete the user. Delete S3 bucket Access System Manager - Session Manager service management console.\nClick the Preferences tab. Click Edit. Scroll down. In the section S3 logging. Uncheck Enable to disable logging. Scroll down. Click Save. Go to S3 service management console\nClick on the S3 bucket we created for this lab. (Example: lab-fcj-bucket-0001 ) Click Empty. Enter permanently delete, then click Empty to proceed to delete the object in the bucket. Click Exit. After deleting all objects in the bucket, click Delete\nEnter the name of the S3 bucket, then click Delete bucket to proceed with deleting the S3 bucket. Delete VPC Endpoints Go to VPC service management console Click Endpoints. Select the 4 endpoints we created for the lab including SSM, SSMMESSAGES, EC2MESSAGES, S3GW. Click Actions. Click Delete VPC endpoints. In the confirm box, enter delete.\nClick Delete to proceed with deleting endpoints. Click the refresh icon, check that all endpoints have been deleted before proceeding to the next step.\nDelete VPC Go to VPC service management console\nClick Your VPCs. Click on Lab VPC. Click Actions. Click Delete VPC. In the confirm box, enter delete to confirm, click Delete to delete Lab VPC and related resources.\n"}, {"uri": "//localhost:1313/5-deployfrontend/5.4-connect-frontend-api-backend/", "title": "Connect the Frontend to the Backend API", "tags": [], "description": "", "content": "Connect the Frontend to the Backend API After uploading your website to S3 and successfully enabling static hosting, the final step is to connect the frontend to the backend through the API Gateway endpoint that you previously tested. This step is crucial for your website to fetch/send dynamic data and function as a modern e-commerce application.\nSteps to follow:\nIdentify the Backend API Endpoint\nUse the API Gateway endpoint obtained in the backend testing step.\nFor example: https://ars3538v1i.execute-api.ap-southeast-1.amazonaws.com/Prod/api/users Update the Endpoint Configuration in the Frontend Source Code\nOpen your frontend project folder. Open the .env file in the Frontend directory. Open the index.js file in the Frontend directory. Paste the copied API Gateway URL into the base URL variable in your code. Conclusion:\nOnce you have successfully connected the frontend to the backend, your website is ready to operate dynamically with real data from AWS. You can proceed to configure a custom domain, enable SSL security, or deploy CloudFront for further optimization.\n"}, {"uri": "//localhost:1313/", "title": "Dynamic E-Commerce Website", "tags": [], "description": "", "content": "Building a Dynamic E-Commerce Website with AWS Serverless Services Overview This Workshop will guide you to deploy a dynamic e-commerce website using the Hugo framework on the AWS cloud platform. You will learn how to set up your environment, configure your AWS account, build your website with <PERSON>, and deploy the entire system using key AWS services such as API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, and leverage the SAM CLI for automated deployment.\nWorkshop Objectives Master essential AWS tools required for a dynamic website project. Learn how to prepare, install, and configure the development environment for a Hugo-based project. Build, package, and deploy a dynamic website with Hugo integrated with modern AWS serverless services. Design and deploy with API Gateway, implement backend logic using Lambda, store data in DynamoDB, and manage the dynamic website on S3. Use CloudFormation to automate the creation and configuration of AWS resources, monitor and manage the system with CloudWatch. Configure domain names and DNS resolution with Route 53 for public access to your dynamic website. Apply DevOps practices to automate the deployment and operation of the website on AWS efficiently. Workshop Outcomes After completing this Workshop, you will:\nClearly understand the architecture and deployment process of a dynamic e-commerce website on AWS. Know how to use AWS services such as API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, and SAM CLI in a real-world project. Be able to build, package, and deploy a dynamic website with <PERSON>, connecting the frontend to the backend via API Gateway and Lambda. Practice dynamic data management with DynamoDB and automate infrastructure with CloudFormation. Configure domain names with Route 53, monitor your system, and log application activity with CloudWatch. Be ready to apply this knowledge to real projects, dynamic website problems, serverless, or DevOps tasks on AWS. Content Introduction Preparation Backend Deployment Backend API Testing with Postman Frontend Deployment Domain &amp; System Monitoring Configuration Frequently Asked Questions Clean up resources "}, {"uri": "//localhost:1313/categories/", "title": "Categories", "tags": [], "description": "", "content": ""}, {"uri": "//localhost:1313/tags/", "title": "Tags", "tags": [], "description": "", "content": ""}]