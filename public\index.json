[{"uri": "//localhost:1313/3-accessibilitytoinstances/3.1-public-instance/", "title": "Connect to Public Instance", "tags": [], "description": "", "content": "\nGo to EC2 service management console. Click on Public Linux Instance. Click Actions. Click Security. Click Modify IAM role. At the Modify IAM role page. Click to select SSM-Role. Click Save. You will need to wait about 10 minutes before performing the next step. This time our EC2 instance will automatically register with the Session Manager.\nGo to the AWS Systems Manager service management console Drag the left menu slider down. Click Session Manager. Click Start Session. Then select Public Linux Instance and click Start session to access the instance. Terminal will appear on the browser. Testing with the command sudo tcpdump -nn port 22 and sudo tcpdump we will see no SSH traffic but only HTTPS traffic. Above, we have created a connection to the public instance without opening SSH port 22, for better security, avoiding any attack to the SSH port.\nOne disadvantage of the above method is that we have to open the Security Group outbound at port 443 to the internet. Since it&rsquo;s a public instance, it probably won&rsquo;t be a problem, but if you want extra security, you can block port 443 to the internet and still use the Session Manager. We will go through this in the private instance section below.\nYou can click terminate to end the currently connected session before proceeding to the next step.\n"}, {"uri": "//localhost:1313/3-accessibilitytoinstances/3.2-private-instance/3.2.2-createvpcendpoint/*******-endpointssm/", "title": "Create Endpoint ssm", "tags": [], "description": "", "content": "Create VPC Endpoint SSM Go to VPC service management console Click Endpoints. Click Create endpoint. At the Create endpoint page. In the Name tag field, enter SSM. In the Service Category section, select AWS Services. In the Service Name section, In the Service category section, select: AWS services In the Service Name section enter: SSM then select Service Name: com.amazonaws.ap-southeast-1.ssm. In the Service Name column, click com.amazonaws.ap-southeast-1.ssm. In the VPC section, select Lab VPC. Select the first AZ, then select the Lab Private Subnet subnet. Scroll down. In the Security Group section, select the Security group SG VPC Endpoint that we created earlier. In the Policy section, select Full access. Scroll down. Click Create endpoint. We have created the VPC Interface Endpoint for SSM. "}, {"uri": "//localhost:1313/", "title": "Dynamic E-Commerce Website", "tags": [], "description": "", "content": "Building a Dynamic E-Commerce Website with AWS Serverless Services Overview This Workshop will guide you to deploy a dynamic e-commerce website using the Hugo framework on the AWS cloud platform. You will learn how to set up your environment, configure your AWS account, build your website with <PERSON>, and deploy the entire system using key AWS services such as API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, and leverage the SAM CLI for automated deployment.\nWorkshop Objectives Master essential AWS tools required for a dynamic website project. Learn how to prepare, install, and configure the development environment for a Hugo-based project. Build, package, and deploy a dynamic website with Hugo integrated with modern AWS serverless services. Design and deploy with API Gateway, implement backend logic using Lambda, store data in DynamoDB, and manage the dynamic website on S3. Use CloudFormation to automate the creation and configuration of AWS resources, monitor and manage the system with CloudWatch. Configure domain names and DNS resolution with Route 53 for public access to your dynamic website. Apply DevOps practices to automate the deployment and operation of the website on AWS efficiently. Workshop Outcomes After completing this Workshop, you will:\nClearly understand the architecture and deployment process of a dynamic e-commerce website on AWS. Know how to use AWS services such as API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, and SAM CLI in a real-world project. Be able to build, package, and deploy a dynamic website with <PERSON>, connecting the frontend to the backend via API Gateway and Lambda. Practice dynamic data management with DynamoDB and automate infrastructure with CloudFormation. Configure domain names with Route 53, monitor your system, and log application activity with CloudWatch. Be ready to apply this knowledge to real projects, dynamic website problems, serverless, or DevOps tasks on AWS. Content Introduction Preparation Backend Deployment Backend API Testing with Postman Frontend Deployment Domain &amp; System Monitoring Configuration Frequently Asked Questions Clean up resources "}, {"uri": "//localhost:1313/3-accessibilitytoinstances/3.2-private-instance/3.2.1-enablevpcdns/", "title": "Enable DNS hostnames", "tags": [], "description": "", "content": "Enable DNS hostnames on VPC. To create VPC Endpoint we will need to enable DNS hostnames feature on VPC. Go to VPC service management console\nClick Your VPCs.\nSelect Lab VPC.\nClick Actions.\nClick Edit DNS hostnames.\nClick Endpoint, then click Create Endpoint.\nAt the Edit DNS hostnames page. Click to select Enable. Click Save changes. "}, {"uri": "//localhost:1313/1-introduce/", "title": "Introduction", "tags": [], "description": "", "content": "System Introduction This workshop will guide you to build and deploy a dynamic e-commerce website using modern AWS serverless services. You will use key components such as API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, and SAM CLI to automate, scale, and optimize the cost of your system.\nOverall Architecture Below is the overall architecture of the system.\n(Insert architecture diagram here)\nFrontend: Uses Hugo theme, hosted statically on Amazon S3 and delivered through CloudFront. Backend: Communicates via RESTful APIs with API Gateway, business logic is handled by AWS Lambda. Database: Uses DynamoDB for dynamic data storage, easily scalable. Infrastructure Management: CloudFormation automatically creates &amp; configures AWS resources. SAM CLI is used for serverless deployment and automation. Domain &amp; Operation Management: Route 53 manages domain/DNS. CloudWatch is used to monitor, log, and provide alerts for the whole system. Data Flow Diagram The following diagram illustrates the main data flow and interactions between system components.\n(Insert data flow or sequence diagram here)\nList of AWS Services Used API Gateway: Creates REST APIs for communication between frontend and backend. S3: Stores the static website and website assets. Lambda: Handles serverless backend logic. DynamoDB: The database. CloudFormation: Automated infrastructure management. Route 53: Domain registration and DNS management. CloudWatch: System monitoring and alerting, supports effective debugging/tracking. SAM CLI: Automates the deployment of serverless resources. Main Features of the Website Fully serverless architecture on AWS. Deploy a dynamic, secure, and automatically scalable website. User-friendly interface with Hugo. Dynamic backend using RESTful APIs via API Gateway &amp; Lambda. Dynamic database with DynamoDB. Real-time data processing with Lambda &amp; DynamoDB. Integrated monitoring, logging, and DNS configuration. Infrastructure as code with CloudFormation &amp; SAM CLI. "}, {"uri": "//localhost:1313/2-prerequiste/2.1-installhugo/", "title": "Preparing VPC and EC2", "tags": [], "description": "", "content": "In this step, we will need to create a VPC with 2 public / private subnets. Then create 1 EC2 Instance Linux located in the public subnet, 1 EC2 Instance Windows located in the private subnet.\nThe architecture overview after you complete this step will be as follows:\nTo learn how to create EC2 instances and VPCs with public/private subnets, you can refer to the lab:\nAbout Amazon EC2 Works with Amazon VPC Content Create VPC Create Public Subnet Create Private Subnet Create security group Create public Linux server Create private Windows server "}, {"uri": "//localhost:1313/4-s3log/4.1-updateiamrole/", "title": "Update IAM Role", "tags": [], "description": "", "content": "For our EC2 instances to be able to send session logs to the S3 bucket, we will need to update the IAM Role assigned to the EC2 instance by adding a policy that allows access to S3.\nUpdate IAM Role Go to IAM service management console Click Roles. In the search box, enter SSM. Click on the SSM-Role role. Click Attach policies. In the Search box enter S3. Click the policy AmazonS3FullAccess. Click Attach policy. In the production environment, we will grant stricter permissions to the specified S3 bucket. In the framework of this lab, we use the policy AmazonS3FullAccess for convenience.\nNext, we will proceed to create an S3 bucket to store session logs.\n"}, {"uri": "//localhost:1313/3-accessibilitytoinstances/3.2-private-instance/", "title": "Connect to Private instance", "tags": [], "description": "", "content": "For Windows instance located in private subnet, there is no public IP, no internet gateway so it cannot go out internet.\nWith this type of instance, the traditional way is to use Bastion host technique which is expensive and laborious, but here we will use Session Manager with this type.\nBasically, the private instance still has to open the TCP 443 port to System Manager, but we don&rsquo;t want to allow connection go out to the internet, but only in its VPC, to enhance our security posture.\nTo do that, we have to include the System Manager endpoint in the VPC, that is, using the VPC interface endpoint:\nVPC interface endpoint is attached to the subnet, so this method can be done not only with private subnet but also with public subnet, meaning that with public subnet, you can completely prohibit TCP 443 go out to the internet.\nContent: Enable DNS hostnames Create VPC Endpoint Connect Private Instance "}, {"uri": "//localhost:1313/3-accessibilitytoinstances/3.2-private-instance/3.2.2-createvpcendpoint/*******-endpointssmmessages/", "title": "Create Endpoint ssmmessages", "tags": [], "description": "", "content": "Create VPC Endpoint SSMMESSAGES Go to VPC service management console Click Endpoints. Click Create endpoint. At the Create endpoint page. In the Name tag field, enter SSMMESSAGES. In the Service Category section, select AWS Services. In the Service Name section, In the Service category select: AWS services In the Service Name field enter: ssmmessages then select Service Name: com.amazonaws.ap-southeast-1.ssmmessages. In the Service Name column, click com.amazonaws.ap-southeast-1.ssmmessages. In the VPC section, select Lab VPC. Select the first AZ, then select the Lab Private Subnet subnet. Scroll down. In the Security Group section, select the Security group SG VPC Endpoint that we created earlier. In the Policy section, select Full access Scroll down. Click Create endpoint. We have created the VPC Interface Endpoint SSMMESSAGES. "}, {"uri": "//localhost:1313/4-s3log/4.2-creates3bucket/", "title": "Create S3 Bucket", "tags": [], "description": "", "content": "In this step, we will create an S3 bucket to store session logs sent from EC2 instances.\nCreate S3 Bucket Access S3 service management console Click Create bucket. At the Create bucket page. In the Bucket name field, enter the bucket name lab-yourname-bucket-0001 In the Region section, select Region you are doing the current lab. The name of the S3 bucket must not be the same as all other S3 buckets in the system. You can substitute your name and enter a random number when generating the S3 bucket name.\nScroll down and click Create bucket. When we created the S3 bucket we did Block all public access so our EC2 instances won&rsquo;t be able to connect to S3 via the internet. In the next step, we will configure the S3 Gateway Endpoint feature to allow EC2 instances to connect to the S3 bucket via the VPC&rsquo;s internal network.\n"}, {"uri": "//localhost:1313/3-accessibilitytoinstances/3.2-private-instance/3.2.2-createvpcendpoint/", "title": "Create VPC Endpoint", "tags": [], "description": "", "content": "Create VPC Endpoint SSM We will create 3 interface endpoints required by the Session Manager:\nInterface endpoints: com.amazonaws.region.ssm com.amazonaws.region.ec2messages com.amazonaws.region.ssmmessages You can refer to more here\nContent: Create Endpoint ssm Create Endpoint ssmmessages Create Endpoint ec2messages "}, {"uri": "//localhost:1313/2-prerequiste/", "title": "Preparation", "tags": [], "description": "", "content": "Overview of &ldquo;Preparation&rdquo; Before you begin deploying your dynamic e-commerce website using AWS services like API Gateway, S3, Lambda, CloudFormation, DynamoDB, Route 53, CloudWatch, and SAM CLI, you need to complete a few basic environment preparation steps. These tools and services will help you quickly, securely, and efficiently deploy your application.\nIn this section, you will perform the necessary setup steps for both the frontend and backend environments of the project:\nInstall Hugo: To build and deploy the frontend of the website. Install NodeJS and Yarn: To support frontend build and management. Install SAM CLI: To deploy the serverless backend on AWS. Create AWS account and configure IAM: To set up access and security for the AWS environment. These preparation steps will ensure that you have the proper development environment in place and working correctly before proceeding with deploying AWS services for your project.\n⚠️ Note: Make sure you have all the tools installed before starting to work with AWS services. Otherwise, you may encounter issues during deployment.\nContent Install Hugo Install NodeJS Install Yarn for frontend Install SAM CLI for backend Create AWS Account &amp; Configure IAM "}, {"uri": "//localhost:1313/2-prerequiste/2.2-installnodejs/", "title": "Preparing VPC and ssssssss", "tags": [], "description": "", "content": "In this step, we will need to create a VPC with 2 public / private subnets. Then create 1 EC2 Instance Linux located in the public subnet, 1 EC2 Instance Windows located in the private subnet.\nThe architecture overview after you complete this step will be as follows:\nTo learn how to create EC2 instances and VPCs with public/private subnets, you can refer to the lab:\nAbout Amazon EC2 Works with Amazon VPC Content Create VPC Create Public Subnet Create Private Subnet Create security group Create public Linux server Create private Windows server "}, {"uri": "//localhost:1313/3-accessibilitytoinstances/", "title": "Connect to EC2 servers", "tags": [], "description": "", "content": "In this step, we will connect to our EC2 servers, located in both the public and private subnets.\nContent 3.1. Connect to EC2 Public Server 3.2. Cconnect to EC2 Private Server\n"}, {"uri": "//localhost:1313/3-accessibilitytoinstances/3.2-private-instance/3.2.3-connectec2/", "title": "Connect to instance", "tags": [], "description": "", "content": "Assign IAM role and restart EC2 instance. Go to EC2 service management console Click Private Windows Instance. Click Actions. Click Security. Click Modify IAM Role. At the Modify IAM Role page. In the IAM role section, select SSM-Role. Click Save. Click Private Windows Instance. Click Instance state. Click Reboot instance to restart, then click Reboot to confirm. Please wait 5 minutes before doing the next step.\nConnect to the private EC2 instance. Go to System Manager - Session Manager service management console Click Start session. Click Private Windows Instance. Click Start session. Type ipconfig command to check the IP address information of Private Windows Instance as shown below. "}, {"uri": "//localhost:1313/3-accessibilitytoinstances/3.2-private-instance/3.2.2-createvpcendpoint/*******-endpointec2messages/", "title": "Create Endpoint ec2messages", "tags": [], "description": "", "content": "Create VPC Endpoint EC2MESSAGES Go to VPC service management console Click Endpoints. Click Create endpoint. At the Create endpoint page. In the Name tag field, enter SSMMESSAGES. In the Service Category section, select AWS Services. In the Service Name section, In the Service category select: AWS services In the field Service Name enter: ec2 then select Service Name: com.amazonaws.ap-southeast-1.ec2messages. In the Service Name column, click com.amazonaws.ap-southeast-1.ec2messages. In the VPC section, select Lab VPC. Select the first AZ, then select the Lab Private Subnet subnet. Scroll down. In the Security Group section, select the Security group SG VPC Endpoint that we created earlier. In the Policy section, select Full access Scroll down. Click Create endpoint. We have created the VPC Interface Endpoint EC2MESSAGES.\nMake sure the 3 required endpoints have been created as shown below.\n"}, {"uri": "//localhost:1313/4-s3log/4.3-creategwes3/", "title": "Create S3 Gateway endpoint", "tags": [], "description": "", "content": " Go to VPC service management console Click Endpoints. Click Create endpoint. At the Create endpoint page. In the Name tag field, enter S3GW. In the Service Category section, click AWS services. In the search box enter S3, then select com.amazonaws.[region].s3 In the Services section, select com.amazonaws.[region].s3 with the Type of Gateway. In the VPC section, select Lab VPC. In the Route tables section, select both route tables. Scroll down, click Create endpoint. The next step is to configure Session Manager to store session logs to the S3 bucket we created.\n"}, {"uri": "//localhost:1313/4-s3log/", "title": "Manage session logs", "tags": [], "description": "", "content": "With Session Manager, we can view the history of connections to instances through Session history. However, we have not seen the details of the commands used in a session.\nIn this section, we will proceed to create an S3 bucket and configure the session logs feature to see the details of the commands used in the session.\nContent: Update IAM Role Create S3 Bucket Create S3 Gateway endpoint Configure Session logs "}, {"uri": "//localhost:1313/4-s3log/4.4-configsessionlogs/", "title": "Monitor session logs", "tags": [], "description": "", "content": "Monitor session logs Access System Manager - Session Manager service management console Click the Preferences tab. Click Edit. Scroll down, at S3 logging, click Enable. Uncheck Allow only encrypted S3 buckets. Click Choose a bucket name from the list. Select the S3 bucket you created. Scroll down, click Save to save the configuration.\nAccess System Manager - Session Manager service management console\nClick Start session. Click Private Windows Instance. Click Start session. Type the command ipconfig. Type the command hostname. Click Terminate to exit the session, click Terminate again to confirm. Check Session logs in S3 Go to S3 service management console Click on the name of the S3 bucket we created for the lab. Click on the object name sessions log On the objects detail page, click Open. Object logs will be opened in a new tab in the browser. You can view the stored commands in session logs. "}, {"uri": "//localhost:1313/testchaper/example/", "title": "Example", "tags": [], "description": "", "content": "Chapter X Some Chapter title Lorem Ipsum.\n"}, {"uri": "//localhost:1313/testchaper/", "title": "TestChaper", "tags": [], "description": "", "content": "Chapter X Some Chapter title Lorem Ipsum.\n"}, {"uri": "//localhost:1313/5-portfwd/", "title": "Port Forwarding", "tags": [], "description": "", "content": "\rPort Forwarding is a useful way to redirect network traffic from one IP address - Port to another IP address - Port. With Port Forwarding we can access an EC2 instance located in the private subnet from our workstation.\nWe will configure Port Forwarding for the RDP connection between our machine and Private Windows Instance located in the private subnet we created for this exercise.\nCreate IAM user with permission to connect SSM Go to IAM service management console Click Users , then click Add users. At the Add user page. In the User name field, enter Portfwd. Click on Access key - Programmatic access. Click Next: Permissions. Click Attach existing policies directly.\nIn the search box, enter ssm. Click on AmazonSSMFullAccess. Click Next: Tags, click Next: Reviews. Click Create user. Save Access key ID and Secret access key information to perform AWS CLI configuration.\nInstall and Configure AWS CLI and Session Manager Plugin To perform this hands-on, make sure your workstation has AWS CLI and Session Manager Plugin installed -manager-working-with-install-plugin.html)\nMore hands-on tutorials on installing and configuring the AWS CLI can be found here.\nWith Windows, when extracting the Session Manager Plugin installation folder, run the install.bat file with Administrator permission to perform the installation.\nImplement Portforwarding Run the command below in Command Prompt on your machine to configure Port Forwarding. aws ssm start-session --target (your ID windows instance) --document-name AWS-StartPortForwardingSession --parameters portNumber=&#34;3389&#34;,localPortNumber=&#34;9999&#34; --region (your region) Windows Private Instance Instance ID information can be found when you view the EC2 Windows Private Instance server details.\nExample command: C:\\Windows\\system32&gt;aws ssm start-session --target i-06343d7377486760c --document-name AWS-StartPortForwardingSession --parameters portNumber=&#34;3389&#34;,localPortNumber=&#34;9999&#34; --region ap-southeast-1 If your command gives an error like below: SessionManagerPlugin is not found. Please refer to SessionManager Documentation here: http://docs.aws.amazon.com/console/systems-manager/session-manager-plugin-not-found\nProve that you have not successfully installed the Session Manager Plugin. You may need to relaunch Command Prompt after installing Session Manager Plugin.\nConnect to the Private Windows Instance you created using the Remote Desktop tool on your workstation. In the Computer section: enter localhost:9999. Return to the administration interface of the System Manager - Session Manager service. Click tab Session history. We will see session logs with Document name AWS-StartPortForwardingSession. Congratulations on completing the lab on how to use Session Manager to connect and store session logs in S3 bucket. Remember to perform resource cleanup to avoid unintended costs.\n"}, {"uri": "//localhost:1313/6-cleanup/", "title": "Clean up resources", "tags": [], "description": "", "content": "We will take the following steps to delete the resources we created in this exercise.\nDelete EC2 instance Go to EC2 service management console\nClick Instances. Select both Public Linux Instance and Private Windows Instance instances. Click Instance state. Click Terminate instance, then click Terminate to confirm. Go to IAM service management console\nClick Roles. In the search box, enter SSM. Click to select SSM-Role. Click Delete, then enter the role name SSM-Role and click Delete to delete the role. Click Users. Click on user Portfwd. Click Delete, then enter the user name Portfwd and click Delete to delete the user. Delete S3 bucket Access System Manager - Session Manager service management console.\nClick the Preferences tab. Click Edit. Scroll down. In the section S3 logging. Uncheck Enable to disable logging. Scroll down. Click Save. Go to S3 service management console\nClick on the S3 bucket we created for this lab. (Example: lab-fcj-bucket-0001 ) Click Empty. Enter permanently delete, then click Empty to proceed to delete the object in the bucket. Click Exit. After deleting all objects in the bucket, click Delete\nEnter the name of the S3 bucket, then click Delete bucket to proceed with deleting the S3 bucket. Delete VPC Endpoints Go to VPC service management console Click Endpoints. Select the 4 endpoints we created for the lab including SSM, SSMMESSAGES, EC2MESSAGES, S3GW. Click Actions. Click Delete VPC endpoints. In the confirm box, enter delete.\nClick Delete to proceed with deleting endpoints. Click the refresh icon, check that all endpoints have been deleted before proceeding to the next step.\nDelete VPC Go to VPC service management console\nClick Your VPCs. Click on Lab VPC. Click Actions. Click Delete VPC. In the confirm box, enter delete to confirm, click Delete to delete Lab VPC and related resources.\n"}, {"uri": "//localhost:1313/categories/", "title": "Categories", "tags": [], "description": "", "content": ""}, {"uri": "//localhost:1313/tags/", "title": "Tags", "tags": [], "description": "", "content": ""}]