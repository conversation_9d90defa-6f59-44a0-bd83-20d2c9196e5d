---
title : "Kết nối frontend với API backend"
date : 2023-10-25
weight : 9
chapter : false
pre : " <b> 5.4 </b> "
---

#### Kết nối frontend với API backend

Sau khi đã upload website lên S3 và bật static hosting thành công, bước cuối cùng là **kết nối frontend với backend thông qua endpoint API Gateway** mà bạn đã kiểm thử trước đó. Đây là thao tác cực kỳ quan trọng để website có thể lấy/gửi dữ liệu động và thực sự hoạt động như một ứng dụng thương mại điện tử hiện đại.

**Các bước thực hiện:**

1. **Xác định endpoint API backend**

   - Sử dụng chính endpoint API Gateway đã lấy ở bước kiểm thử backend.  
     Ví dụ:
     ```
     https://abcd1234.execute-api.ap-southeast-1.amazonaws.com/dev/
     ```

2. **Cập nhật cấu hình endpoint vào mã nguồn frontend**

   - Với React/Vue/Angular, thường sẽ có file cấu hình `.env` hoặc biến môi trường để định nghĩa URL backend.
   - Ví dụ (React):
     - Tạo hoặc cập nhật file `.env` trong thư mục dự án:
       ```
       REACT_APP_API_URL=https://abcd1234.execute-api.ap-southeast-1.amazonaws.com/dev/
       ```
     - Trong code, sử dụng biến này khi gọi API:
       ```js
       fetch(`${process.env.REACT_APP_API_URL}users`, { ... })
       ```
   - Build lại frontend để các thay đổi có hiệu lực:
     ```sh
     yarn build
     ```
   - Upload lại bản build mới lên S3 như đã hướng dẫn ở các bước trước.

3. **Kiểm thử fetch API từ frontend**

   - Truy cập website bằng đường dẫn S3 static hosting.
   - Thực hiện thao tác fetch dữ liệu (VD: đăng nhập, lấy danh sách sản phẩm...) trên giao diện.
   - Mở **Developer Tools** (F12) → tab **Network** để kiểm tra request API gửi đi và kết quả trả về.
   - Nếu thấy dữ liệu trả về đúng từ backend, việc kết nối đã thành công.

4. **Xử lý các lỗi phổ biến khi kết nối**

   - **Lỗi CORS (Cross-Origin Resource Sharing):**
     - Nếu thấy lỗi liên quan đến CORS khi gọi API (bị chặn bởi trình duyệt), kiểm tra lại:
       - Policy CORS trên API Gateway (và cả trên bucket S3 nếu fetch file).
       - Đảm bảo header Access-Control-Allow-Origin đã trả về đúng domain frontend hoặc dùng dấu `*` (nếu test nội bộ).
   - **Lỗi 403/404 hoặc không trả về dữ liệu:**
     - Kiểm tra lại endpoint URL, route resource đã đúng chưa.
     - Kiểm tra log Lambda trên CloudWatch để xác định lỗi backend nếu có.
   - **Website không load version mới:**  
     - Có thể do cache trình duyệt, hãy refresh mạnh (Ctrl+F5) hoặc xóa cache, hoặc đặt lại cache-control cho index.html.

{{% notice tip %}}
**Mẹo:**  
Nên cấu hình biến môi trường backend API endpoint, tránh hardcode trực tiếp vào source code để dễ thay đổi khi chuyển môi trường (dev, staging, production).
{{% /notice %}}

**Kết luận:**  
Sau khi kết nối frontend với backend thành công, website đã sẵn sàng hoạt động động với dữ liệu thực tế từ AWS. Bạn có thể tiếp tục cấu hình domain, bảo mật SSL, hoặc triển khai CloudFront để tối ưu hơn.
