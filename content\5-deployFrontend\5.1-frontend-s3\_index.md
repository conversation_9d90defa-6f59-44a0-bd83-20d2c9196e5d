---
title : "Deploying the Frontend to S3 Bucket"
date : 2023-10-25
weight : 6
chapter : false
pre : " <b> 5.1 </b> "
---

### Deploying the Frontend to S3 Bucket

To bring your frontend website online, you will upload all the built source files to **AWS S3 Bucket**. S3 acts as a static web host, allowing you to serve your website with low cost and high speed.

**Steps to follow:**

1. **Build the frontend source code**

   - Open the `terminal` in VSCode and navigate to the `frontend` directory.
   - Copy and paste the following command into the terminal:
     ```
     yarn build
     ```
   - The `build` folder will contain all the static files to upload.
   - Wait about 1 minute for the build process to complete.

![yarn build](/images/yarn_build.png)

2. **Create a new S3 Bucket on AWS**

   - Go to the [AWS S3 Console](https://s3.console.aws.amazon.com/s3).
   - Click **Create bucket**.
   - Enter a bucket name, e.g., `my-frontend-bucket-2025`.
   - **Region:** Choose the same region as your backend (recommended: ap-southeast-1 for consistency).
   - **Disable “Block all public access”**.
   - Check **I acknowledge that the current settings might result in this bucket and the objects within becoming public.** to confirm the warning so your website can be public.
   - Click **Create bucket** to finish.

![bucket_my_frontend](/images/create_bucket_my_frontend_s3.png)

{{% notice warning %}}
**Caution:**  
A bucket hosting a static website must have public read access for everyone. However, do NOT upload any sensitive data here because anyone with the link can access it!
{{% /notice %}}

3. **Grant public read permissions to the bucket**

   - After creating the bucket, proceed to grant public access.
   - Click the bucket you just created.

![click_bucket_myfrontend](/images/click_bucket_myfrontend.png)

   - Go to the **Permissions** tab of the bucket.
   - Scroll down to find **Bucket policy**.
   - Click **Edit**.

![click_edit_bucket_policy](/images/click_edit_bucket_policy.png)

   - Add a policy to allow public read access, for example:
     ```json
     {
       "Version": "2012-10-17",
       "Statement": [
         {
           "Sid": "PublicReadGetObject",
           "Effect": "Allow",
           "Principal": "*",
           "Action": "s3:GetObject",
           "Resource": "arn:aws:s3:::my-frontend-bucket-2025/*"
         }
       ]
     }
     ```

   - Click Save to apply.

![click_edit_bucket_policy](/images/set_permisson_bucket_myfrontend.png)

4. **Upload the built source code to the bucket using AWS CLI**

   - Ensure you have installed and configured AWS CLI (run `aws configure`).
   - Run the following commands:
        ```
        cd frontend
        aws s3 cp build/ s3://my-frontend-bucket-2025/ --recursive
        ```
![build_s3_to_frontend](/images/build_s3_to_frontend.png)

   - The `--recursive` flag uploads all files and subfolders inside the build directory.

5. **Verify bucket contents**

   - Go back to the bucket on AWS Console and confirm that all files (index.html, main.js, CSS, images, etc.) have been uploaded successfully.
   - You can click directly on a file (e.g., `index.html`) and copy the **Object URL** to check if the file is publicly accessible (you should be able to view the HTML/raw file in your browser).

   - You can also check all files and folders uploaded to S3 by browsing the Objects section.

![build_s3_to_frontend](/images/file_uploaded_s3_after_build.png)

   **Conclusion:**  
   After completing these steps, your frontend website is now uploaded to AWS S3 and ready for enabling static hosting in the next step.
