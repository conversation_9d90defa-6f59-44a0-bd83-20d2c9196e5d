---
title : "<PERSON>"
date : 2023-10-25
weight : 1
chapter : false
pre : " <b> 2.1 </b> "
---

### <PERSON><PERSON><PERSON> đặt <PERSON> l<PERSON> một static site generator n<PERSON><PERSON> chón<PERSON>, mạnh mẽ và dễ sử dụng. Trong bư<PERSON><PERSON> nà<PERSON>, bạn sẽ cài đặt Hugo để xây dựng và triển khai giao diện cho website thương mại điện tử động của mình.

#### Bước 1: Cài đặt Hugo

1. **Tải và cài đặt Hugo:**
   - T<PERSON>y cập vào trang chính của Hugo tại: [Hugo Releases](https://github.com/gohugoio/hugo/releases).
   - Chọn phiên bản phù hợp với hệ điều hành của bạn (Windows, macOS, hoặc Linux).
   - Tải file **.zip** hoặc **.tar.gz** về và giải nén vào thư mục bạn chọn.

2. **<PERSON><PERSON><PERSON> đặt <PERSON> trên Windows:**
   - T<PERSON><PERSON> bản **.zip** củ<PERSON> từ trang [Hugo for Windows](https://github.com/gohugoio/hugo/releases/latest).
   - Giải nén và di chuyển tệp `hugo.exe` vào thư mục mà bạn dễ dàng truy cập (ví dụ: `C:\Hugo`).
   - Thêm đường dẫn thư mục vào **PATH**:
     - Vào **Control Panel** → **System and Security** → **System** → **Advanced system settings**.
     - Nhấn **Environment Variables** → Chọn **Path** và nhấn **Edit**.
     - Thêm thư mục chứa `hugo.exe` vào.
   
   **Kiểm tra cài đặt:**
   - Mở **Command Prompt** và nhập lệnh sau:
     ```bash
     hugo version
     ```
     Nếu mọi thứ cài đặt thành công, bạn sẽ thấy thông tin phiên bản Hugo hiện ra.

3. **Cài đặt Hugo trên macOS:**
   - Cài đặt qua **Homebrew**:
     ```bash
     brew install hugo
     ```

4. **Cài đặt Hugo trên Linux (Ubuntu):**
   - Sử dụng lệnh `apt`:
     ```bash
     sudo apt-get install hugo
     ```

{{% notice success %}}
Hugo đã được cài đặt thành công! Bạn có thể bắt đầu tạo website của mình với theme đã chọn.
{{% /notice %}}

#### Bước 2: Tạo Project Hugo Mới

Sau khi cài đặt thành công Hugo, bạn có thể tạo một project Hugo mới với các bước sau:

1. **Tạo thư mục cho project:**
   - Mở terminal (hoặc Command Prompt trên Windows) và di chuyển đến thư mục bạn muốn tạo project.
   - Chạy lệnh sau để tạo project Hugo mới:
     ```bash
     hugo new site my-ecommerce-website
     ```
     Lệnh này sẽ tạo một thư mục `my-ecommerce-website` chứa các file cấu hình và thư mục cần thiết cho dự án.

2. **Chọn và cài đặt theme:**
   - Truy cập trang [Hugo Themes](https://themes.gohugo.io/) và chọn một theme mà bạn thích.
   - Cài đặt theme bằng cách clone repository của theme vào thư mục `themes` trong project của bạn:
     ```bash
     cd my-ecommerce-website
     git init
     git submodule add https://github.com/gohugoio/hugo-theme-ananke.git themes/ananke
     ```

3. **Chạy thử website:**
   - Sau khi cài đặt theme, bạn có thể chạy Hugo để xem website của mình trên máy local bằng cách sử dụng lệnh sau:
     ```bash
     hugo server
     ```
   - Truy cập vào `http://localhost:1313` để xem website.

### Các bước tiếp theo
Sau khi cài đặt và chạy Hugo thành công, bạn có thể bắt đầu chỉnh sửa nội dung, cấu hình các trang và thêm các tính năng cho website thương mại điện tử của mình.

### Lưu ý quan trọng:
- Đảm bảo rằng bạn đã cài đặt Hugo đúng cách trên hệ thống của mình. Nếu có vấn đề, hãy kiểm tra lại đường dẫn và cài đặt.
- Hugo là một công cụ mạnh mẽ cho việc xây dựng các website tĩnh, nhưng trong dự án này, bạn sẽ kết hợp nó với các dịch vụ AWS để triển khai website động.

{{% notice info %}}
Hãy tiếp tục với các bước tiếp theo để cài đặt các công cụ cần thiết cho frontend và backend.
{{% /notice %}}
